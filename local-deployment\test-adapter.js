#!/usr/bin/env node

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

async function testAdapterSetup() {
    console.log('\n[TEST] Testing MyGeotab Adapter Setup');
    console.log('='.repeat(40));

    // Test database connection (using admin user for comprehensive testing)
    const client = new Client({
        host: 'localhost',
        port: 5432,
        database: 'geotabadapterdb',
        user: 'pgadmin',
        password: 'localdev123'
    });

    try {
        await client.connect();
        console.log('[OK] Database connection successful');

        // Check if official tables exist
        const tables = [
            'Devices2',
            'LogRecords2', 
            'StatusData2',
            'FaultData2',
            'Trips2',
            'Users2'
        ];

        console.log('\n[TABLES] Checking Official Geotab Tables:');
        for (const table of tables) {
            try {
                const result = await client.query(`SELECT COUNT(*) FROM "${table}"`);
                console.log(`[OK] ${table}: ${result.rows[0].count} records`);
            } catch (error) {
                console.log(`[ERROR] ${table}: Table not found or error`);
            }
        }

        // Check configuration file
        console.log('\n[CONFIG] Checking Configuration:');
        const configPath = path.join(__dirname, 'official-adapter', 'MyGeotabAPIAdapter_SCD_win-x64', 'appsettings.json');

        if (fs.existsSync(configPath)) {
            const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

            console.log(`[OK] Config file exists`);
            console.log(`[INFO] Database Provider: ${config.DatabaseSettings.DatabaseProviderType}`);
            console.log(`[INFO] MyGeotab Server: ${config.LoginSettings.MyGeotabServer}`);
            console.log(`[INFO] MyGeotab Database: ${config.LoginSettings.MyGeotabDatabase}`);

            // Check if credentials are configured
            if (config.LoginSettings.MyGeotabUser === 'PLACEHOLDER_USER') {
                console.log('[WARNING] MyGeotab credentials not configured. Run: npm run configure-mygeotab');
            } else {
                console.log('[OK] MyGeotab credentials configured');
            }

            // Check enabled feeds
            const feeds = config.AppSettings.Feeds;
            console.log('\n[FEEDS] Enabled Data Feeds:');
            Object.keys(feeds).forEach(feedName => {
                const feed = feeds[feedName];
                const enabledKey = Object.keys(feed).find(key => key.startsWith('Enable'));
                if (enabledKey && feed[enabledKey]) {
                    console.log(`[OK] ${feedName}: ${feed[enabledKey] ? 'Enabled' : 'Disabled'}`);
                }
            });

        } else {
            console.log('[ERROR] Configuration file not found');
        }

        // Check adapter executable
        console.log('\n[FILES] Checking Adapter Files:');
        const adapterPath = path.join(__dirname, 'official-adapter', 'MyGeotabAPIAdapter_SCD_win-x64', 'MyGeotabAPIAdapter.exe');
        if (fs.existsSync(adapterPath)) {
            console.log('[OK] MyGeotab Adapter executable found');
        } else {
            console.log('[ERROR] MyGeotab Adapter executable not found');
        }

        console.log('\n[NEXT] Next Steps:');
        console.log('1. Configure MyGeotab credentials: npm run configure-mygeotab');
        console.log('2. Start the adapter: npm run start-adapter (calls start-adapter.bat)');
        console.log('   Alternative: start-adapter.bat (direct execution)');
        console.log('3. Monitor in Grafana: http://localhost:3000');

    } catch (error) {
        console.error('[ERROR] Database connection failed:', error.message);
        console.log('\n[TIP] Make sure PostgreSQL is running: docker-compose up postgres -d');
    } finally {
        await client.end();
    }
}

// Run the test
testAdapterSetup().catch(console.error);
