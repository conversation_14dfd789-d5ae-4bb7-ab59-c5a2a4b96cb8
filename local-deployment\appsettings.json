{"DatabaseSettings": {"DatabaseProviderType": "PostgreSQL", "ConnectionString": "Host=localhost;Port=5432;Database=geotabadapterdb;Username=geotabadapter_client;Password=localdev123;", "UncategorizedDatabaseWriteOperationTimeoutSeconds": 30, "DatabaseCommandTimeoutSeconds": 60, "DatabaseConnectionTestTimeoutSeconds": 20, "DataOptimizerEnabled": true, "DataOptimizerProcessingIntervalMinutes": 60}, "AppSettings": {"DisableMachineNameValidation": true, "BypassAdapterDatabaseInitializationCheck": false, "EnableMinimumIntervalSamplingForLogRecords": false, "EnableMinimumIntervalSamplingForStatusData": false, "TrackZoneStops": true, "EnableDebugMode": false, "TimeoutSecondsForDatabaseTasks": 600, "TimeoutSecondsForMyGeotabTasks": 300, "MessageQueueingEnabled": false, "MessageQueueingBatchSize": 1000, "MessageQueueingIntervalMilliseconds": 1000}, "LoginSettings": {"MyGeotabServer": "my.geotab.com", "MyGeotabDatabase": "goac", "MyGeotabUser": "<EMAIL>", "MyGeotabPassword": "kjldsahfkjh8932475kjdsahf"}, "FeedSettings": {"ExceptionEventFeedSettings": {"FeedEnabled": true, "FeedIntervalSeconds": 30}, "FaultDataFeedSettings": {"FeedEnabled": true, "FeedIntervalSeconds": 30}, "LogRecordFeedSettings": {"FeedEnabled": true, "FeedIntervalSeconds": 30}, "StatusDataFeedSettings": {"FeedEnabled": true, "FeedIntervalSeconds": 30}, "TripFeedSettings": {"FeedEnabled": true, "FeedIntervalSeconds": 30}, "BinaryDataFeedSettings": {"FeedEnabled": false, "FeedIntervalSeconds": 30}, "ChargeEventFeedSettings": {"FeedEnabled": false, "FeedIntervalSeconds": 30}, "DebugDataFeedSettings": {"FeedEnabled": false, "FeedIntervalSeconds": 30}, "DeviceStatusInfoFeedSettings": {"FeedEnabled": false, "FeedIntervalSeconds": 30}, "DriverChangeFeedSettings": {"FeedEnabled": false, "FeedIntervalSeconds": 30}, "DutyStatusAvailabilityFeedSettings": {"FeedEnabled": false, "FeedIntervalSeconds": 30}, "DVIRLogFeedSettings": {"FeedEnabled": false, "FeedIntervalSeconds": 30}}}