# MyGeotab Adapter System - Operational Guide

## System Overview

**Architecture:** MyGeotab API → Official .NET Adapter → PostgreSQL → Grafana

**Components:**
- **Official MyGeotab Adapter** (.NET 9.0) - Real-time data synchronization
- **PostgreSQL** - Data storage with optimized configuration
- **Grafana** - Advanced dashboards and visualization
- **PgHero** - Database performance monitoring
- **Dozzle** - Container log monitoring

## Quick Start Commands

### Essential Operations
```bash
# Quick setup (new deployments)
npm run quick-setup

# Check system status
npm run system-status

# Start adapter with process isolation
npm run start-adapter-service

# Stop adapter service
npm run stop-adapter-service

# Restart adapter
npm run restart-adapter

# Performance monitoring
npm run performance

# Monitor adapter continuously
npm run monitor-adapter

# Quick health check
npm run quick-check

# Start all Docker services
npm run up

# View real-time logs
npm run logs
```

### Configuration
```bash
# Configure MyGeotab credentials
npm run configure-mygeotab

# Reset entire system (removes all data)
npm run reset
```

## Service URLs

| Service | URL | Credentials |
|---------|-----|-------------|
| **Grafana** | http://localhost:3000 | admin/admin123 |
| **PgHero** | http://localhost:8081 | admin/pghero123 |
| **Dozzle** | http://localhost:8080 | No auth required |
| **PostgreSQL** | localhost:5432 | pgadmin/localdev123 (admin) |

## Startup Sequence

### 1. Initial Setup (One-time)
```bash
# 1. Start Docker infrastructure
npm run up

# 2. Setup database users and schema
setup-postgres-users.bat

# 3. Configure MyGeotab credentials
npm run configure-mygeotab

# 4. Test setup
npm run test-adapter
```

### 2. Daily Operations
```bash
# Start adapter service (recommended)
npm run start-adapter-service

# OR start adapter directly
npm run start-adapter
```

### 3. Monitoring
```bash
# Check system status
npm run system-status

# View logs
npm run logs

# Monitor in Grafana
# Open: http://localhost:3000
```

## Process Management

### Enhanced Service Scripts

**start-adapter-service.bat** - Recommended startup method

- Checks prerequisites automatically
- Verifies database connectivity
- Starts adapter in isolated window
- Provides monitoring URLs
- Prevents duplicate processes

**stop-adapter-service.bat** - Graceful shutdown

- Gracefully stops adapter process
- Verifies successful termination
- Preserves Docker services
- Provides restart instructions

**system-status.bat** - Comprehensive health check

- Docker services status
- Adapter process verification
- Service connectivity tests
- Database status check
- Quick action commands

### Process Isolation Benefits
- **Dedicated Window:** Adapter runs in separate terminal
- **Persistent Operation:** Continues running when main terminal closes
- **Easy Monitoring:** Clear process identification
- **Graceful Shutdown:** Proper cleanup on termination

## Data Flow Monitoring

### Real-time Data Verification
```bash
# Check data synchronization
npm run test-adapter

# Expected output:
# ✅ LogRecords2: [increasing count]
# ✅ StatusData2: [increasing count]
# ✅ Devices2: [stable count]
# ✅ Users2: [stable count]
```

### Data Feed Configuration
- **LogRecord Feed:** 30-second intervals (GPS data)
- **StatusData Feed:** 30-second intervals (Diagnostics)
- **ExceptionEvent Feed:** 30-second intervals (Alerts)
- **FaultData Feed:** 30-second intervals (Fault codes)
- **Trip Feed:** 30-second intervals (Trip data)

## Troubleshooting

### Common Issues

**1. Adapter Won't Start**
```bash
# Check prerequisites
npm run system-status

# Verify Docker services
npm run up

# Test database
npm run test-adapter

# Check for existing processes
tasklist | findstr MyGeotabAPIAdapter
```

**2. No Data Flowing**
```bash
# Verify MyGeotab credentials
npm run configure-mygeotab

# Check adapter logs
npm run logs

# Test database connection
npm run test-adapter
```

**3. Service Connectivity Issues**
```bash
# Restart Docker services
npm run down
npm run up

# Wait for health checks
timeout /t 30

# Verify status
npm run system-status
```

### Health Check Indicators

**✅ Healthy System:**
- All Docker services show "healthy" status
- MyGeotab Adapter process running
- Database record counts increasing
- All service URLs accessible

**❌ Unhealthy System:**
- Docker services showing "unhealthy"
- No MyGeotab Adapter process
- Static database record counts
- Service URLs not accessible

## Performance Optimization

### Database Configuration
- **Shared Buffers:** 256MB
- **Effective Cache Size:** 1GB
- **Max Connections:** 100
- **Maintenance Work Mem:** 64MB

### Resource Limits
- **PostgreSQL:** 512MB limit, 256MB reserved
- **Health Checks:** 30-second intervals
- **Restart Policy:** unless-stopped

### Monitoring Recommendations
1. **Daily:** Check system-status for health
2. **Weekly:** Review PgHero for database performance
3. **Monthly:** Check Dozzle logs for errors
4. **Quarterly:** Review Grafana dashboards for trends

## Backup and Recovery

### Data Backup
```bash
# Stop adapter
npm run stop-adapter-service

# Backup database
docker exec postgres pg_dump -U pgadmin geotabadapterdb > backup.sql

# Restart adapter
npm run start-adapter-service
```

### System Recovery
```bash
# Complete system reset
npm run down
npm run reset

# Reconfigure
npm run configure-mygeotab
npm run start-adapter-service
```

## Security Considerations

- **Database Access:** Limited to localhost only
- **Service Authentication:** Grafana and PgHero require login
- **Network Isolation:** Services run in isolated Docker network
- **Credential Management:** Stored in configuration files only

## Support and Maintenance

### Log Locations
- **Adapter Logs:** Visible in dedicated window or Dozzle
- **Docker Logs:** `npm run logs`
- **Database Logs:** Available through PgHero

### Regular Maintenance
- **Weekly:** Review system status and performance
- **Monthly:** Check for adapter updates
- **Quarterly:** Database maintenance and optimization

### Getting Help
1. Run `npm run system-status` for current state
2. Check logs with `npm run logs`
3. Verify configuration with `npm run test-adapter`
4. Review this guide for troubleshooting steps
