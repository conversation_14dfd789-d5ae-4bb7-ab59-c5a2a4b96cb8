﻿// This file is used by Code Analysis to maintain SuppressMessage
// attributes that are applied to this project.
// Project-level suppressions either have no target or are given
// a specific target and scoped to a namespace, type, member, etc.

using System.Diagnostics.CodeAnalysis;

[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.MyGeotabAPI.MyGeotabAPIHelper.GetAsync``1(Geotab.Checkmate.ObjectModel.Search,System.Int32)~System.Threading.Tasks.Task{System.Collections.Generic.IList{``0}}")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.MyGeotabAPI.MyGeotabAPIHelper.GetFeedAsync``1(System.Nullable{System.DateTime},System.Int32,System.Int32)~System.Threading.Tasks.Task{Geotab.Checkmate.ObjectModel.FeedResult{``0}}")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.MyGeotabAPI.MyGeotabAPIHelper.GetFeedAsync``1(System.Nullable{System.Int64},System.Int32,System.Int32)~System.Threading.Tasks.Task{Geotab.Checkmate.ObjectModel.FeedResult{``0}}")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.MyGeotabAPI.MyGeotabAPIHelper.GetVersionInformationAsync(System.Int32)~System.Threading.Tasks.Task{Geotab.Checkmate.ObjectModel.VersionInformation}")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.MyGeotabAPI.MyGeotabAPIHelper.SetAsync``1(``0,System.Int32)~System.Threading.Tasks.Task{System.Object}")]
[assembly: SuppressMessage("Style", "IDE0074:Use compound assignment", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.MyGeotabAPI.MyGeotabAPIHelper.SetAsyncPolicyWrapForMyGeotabAPICalls(System.Int32)")]
