﻿using Geotab.Checkmate.ObjectModel;
using Geotab.Checkmate.ObjectModel.Charging;
using Geotab.Checkmate.ObjectModel.Engine;
using Geotab.Checkmate.ObjectModel.Exceptions;
using MyGeotabAPIAdapter.Configuration;
using MyGeotabAPIAdapter.Helpers;
using MyGeotabAPIAdapter.MyGeotabAPI;
using NLog;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace MyGeotabAPIAdapter
{
    /// <summary>
    /// A generic class that handles processing of Geotab <see cref="Entity"/>s that can be classified as "feed data". These object types typically represent data points generated by the telematics devices and vehicles. Examples include <see cref="LogRecord"/>, <see cref="StatusData"/> and <see cref="FaultData"/>.
    /// </summary>
    /// <typeparam name="T">The type of Geotab <see cref="Entity"/> to be processed by this <see cref="GenericGeotabObjectFeeder{T}"/> instance.</typeparam>
    internal class GenericGeotabObjectFeeder<T> : IGenericGeotabObjectFeeder<T> where T : class, IEntity
    {
        // Obtain the type parameter type (for logging purposes).
        readonly Type typeParameterType = typeof(T);

        static string CurrentClassName { get => $"{nameof(GenericGeotabObjectFeeder<T>)}<{typeof(T).Name}>"; }

        const int DefaultFeedPollingIntervalSeconds = 2;
        // According to <see href="https://geotab.github.io/sdk/software/api/reference/#M:Geotab.Checkmate.Database.DataStore.GetFeed1">GetFeed(...)</see>, 5000 is the lowest feed result limit; thus, it is used here as the default, but should be overridden with the correct limit for the subject entity type.
        const int DefaultFeedResultsLimit = 5000;
        const long DefaultLastFeedVersion = 0;
        // FeedCurrentThresholdRecordCount is used to set FeedContainer.FeedCurrent; if the FeedResult returned in a GetFeed call contains less than this number of entities, the feed will be considered up-to-date.
        const int FeedCurrentThresholdRecordCount = 1000;
        // According to the Geotab <see href="https://geotab.github.io/sdk/software/guides/concepts/#rate-limits">Rate Limits</see> documentation, the GetFeed method has a rate limit of 1 request per second. Add a 10 millisecond buffer to account for any rounding related issues that might occur and use this to ensure the GetFeed method isn't called too often.
        const int GeotabMinimumGetFeedIntervalMilliseconds = 1010;

        readonly AdapterService adapterServiceType = AdapterService.NullValue;
        readonly SemaphoreSlim initializationLock = new(1, 1);

        readonly IAdapterConfiguration adapterConfiguration;
        readonly IDateTimeHelper dateTimeHelper;
        readonly IMyGeotabAPIHelper myGeotabAPIHelper;

        readonly Logger logger = LogManager.GetCurrentClassLogger();

        /// <inheritdoc/>
        public AdapterService AdapterServiceType { get => adapterServiceType; }

        /// <inheritdoc/>
        public FeedStartOption ConfiguredFeedStartOption { get; private set; }

        /// <inheritdoc/>
        public bool FeedCurrent { get; private set; }

        /// <inheritdoc/>
        public int FeedPollingIntervalSeconds { get; private set; }

        /// <inheritdoc/>
        public ConcurrentDictionary<Id, T> FeedResultData { get; private set; }

        /// <inheritdoc/>
        public int FeedResultsLimit { get; private set; }

        /// <inheritdoc/>
        public FeedStartOption FeedStartOption { get; private set; }

        /// <inheritdoc/>
        public DateTime FeedStartTimeUtc { get; private set; }

        /// <inheritdoc/>
        public string Id { get; private set; }

        /// <inheritdoc/>
        public bool IsInitialized { get; private set; }

        /// <inheritdoc/>
        public DateTime LastFeedRetrievalTimeUtc { get; set; }

        /// <inheritdoc/>
        public long? LastFeedVersion { get; set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="GenericGeotabObjectFeeder{T}"/> class.
        /// </summary>
        public GenericGeotabObjectFeeder(IAdapterConfiguration adapterConfiguration, IDateTimeHelper dateTimeHelper, IMyGeotabAPIHelper myGeotabAPIHelper)
        {
            this.adapterConfiguration = adapterConfiguration;
            this.dateTimeHelper = dateTimeHelper;
            this.myGeotabAPIHelper = myGeotabAPIHelper;
            adapterServiceType = GetAdapterServiceMemberForT();
            FeedResultData = new ConcurrentDictionary<Id, T>();
            ConfiguredFeedStartOption = adapterConfiguration.FeedStartOption;
            FeedStartOption = ConfiguredFeedStartOption;
            FeedStartTimeUtc = adapterConfiguration.FeedStartSpecificTimeUTC;

            // Set default values:
            FeedPollingIntervalSeconds = DefaultFeedPollingIntervalSeconds;
            FeedResultsLimit = DefaultFeedResultsLimit;
            LastFeedVersion = DefaultLastFeedVersion;

            Id = Guid.NewGuid().ToString();
            logger.Debug($"{nameof(GenericGeotabObjectFeeder<T>)}<{typeParameterType}> [Id: {Id}] created.");
        }

        /// <summary>
        /// Determines the <see cref="AdapterService"/> member associated with the type of Geotab <see cref="Entity"/> to be processed by this <see cref="GenericGeotabObjectFeeder{T}"/> instance. If no <see cref="AdapterService"/> member is found a <see cref="NotSupportedException"/> is thrown.
        /// </summary>
        AdapterService GetAdapterServiceMemberForT()
        {
            if (typeParameterType == typeof(Geotab.Checkmate.ObjectModel.BinaryData)) { return AdapterService.BinaryDataProcessor; }
            if (typeParameterType == typeof(ChargeEvent)) { return AdapterService.ChargeEventProcessor; }
            if (typeParameterType == typeof(Controller)) { return AdapterService.ControllerProcessor; }
            if (typeParameterType == typeof(DebugData)) { return AdapterService.DebugDataProcessor; }
            if (typeParameterType == typeof(Device)) { return AdapterService.DeviceProcessor; }
            if (typeParameterType == typeof(DeviceStatusInfo)) { return AdapterService.DeviceStatusInfoProcessor; }
            if (typeParameterType == typeof(Diagnostic)) { return AdapterService.DiagnosticProcessor; }
            if (typeParameterType == typeof(DriverChange)) { return AdapterService.DriverChangeProcessor; }
            if (typeParameterType == typeof(DutyStatusAvailability)) { return AdapterService.DutyStatusAvailabilityProcessor; }
            if (typeParameterType == typeof(DutyStatusLog)) { return AdapterService.DutyStatusLogProcessor; }
            if (typeParameterType == typeof(DVIRLog)) { return AdapterService.DVIRLogProcessor; }
            if (typeParameterType == typeof(ExceptionEvent)) { return AdapterService.ExceptionEventProcessor; }
            if (typeParameterType == typeof(FailureMode)) { return AdapterService.FailureModeProcessor; }
            if (typeParameterType == typeof(FaultData)) { return AdapterService.FaultDataProcessor; }
            if (typeParameterType == typeof(Group)) { return AdapterService.GroupProcessor; }
            if (typeParameterType == typeof(LogRecord)) { return AdapterService.LogRecordProcessor; }
            if (typeParameterType == typeof(Rule)) { return AdapterService.RuleProcessor; }
            if (typeParameterType == typeof(StatusData)) { return AdapterService.StatusDataProcessor; }
            if (typeParameterType == typeof(Trip)) { return AdapterService.TripProcessor; }
            if (typeParameterType == typeof(UnitOfMeasure)) { return AdapterService.UnitOfMeasureProcessor; }
            if (typeParameterType == typeof(User)) { return AdapterService.UserProcessor; }
            if (typeParameterType == typeof(Zone)) { return AdapterService.ZoneProcessor; }
            if (typeParameterType == typeof(ZoneType)) { return AdapterService.ZoneTypeProcessor; }

            var errorMessage = $"No '{nameof(AdapterService)}' is associated with the type of Geotab '{nameof(Entity)}' ('{typeParameterType}') to be processed by this '{nameof(GenericGeotabObjectFeeder<T>)}' instance.";
            logger.Error(errorMessage);
            throw new NotSupportedException(errorMessage);
        }

        /// <inheritdoc/>
        public async Task GetFeedDataBatchAsync(CancellationTokenSource cancellationTokenSource)
        {
            _ = cancellationTokenSource.Token;

            CancellationToken cancellationToken = cancellationTokenSource.Token;

            ValidateInitialized();

            // Only poll the data feed if the configured polling interval has elapsed or if the feed is not current (e.g. when starting at some point in the past and numerous GetFeed calls are required to pull all of the historic data).
            if (dateTimeHelper.TimeIntervalHasElapsed(LastFeedRetrievalTimeUtc, DateTimeIntervalType.Seconds, FeedPollingIntervalSeconds) || FeedCurrent == false)
            {
                logger.Debug($"{typeParameterType.Name} data feed poll required.");

                // If FeedCurrent = false, GetFeed will be called as quickly as possible until the data feed is caught-up to near real-time. However, it is possible that this can happen so quickly that the GetFeed rate limit is exceeded. Add some throttling logic to make sure that this does not happen.
                while (dateTimeHelper.TimeIntervalHasElapsed(LastFeedRetrievalTimeUtc, DateTimeIntervalType.Milliseconds, GeotabMinimumGetFeedIntervalMilliseconds) == false)
                {
                    await Task.Delay(5);
                }

                // Clear any previous FeedResult data.
                FeedResultData.Clear();

                cancellationToken.ThrowIfCancellationRequested();

                // Execute the GetFeedAsync method according to the specifed FeedStartOption. Note that CurrentTime and SpecificTime are only for use in an initial GetFeedAsync call and all subsequent GetFeedAsync calls should use the FeedVersion option.
                logger.Debug($"Calling GetFeedAsync<{typeParameterType.Name}>.");
                FeedResult<T> feedResult = null;

                switch (FeedStartOption)
                {
                    case FeedStartOption.CurrentTime:
                        // GetFeed *search not supported for DeviceStatusInfo. 
                        if (typeParameterType == typeof(DeviceStatusInfo))
                        {
                            feedResult = await myGeotabAPIHelper.GetFeedAsync<T>(LastFeedVersion, FeedResultsLimit, adapterConfiguration.TimeoutSecondsForMyGeotabTasks);

                            // Switch to FeedVersion for subsequent calls.
                            FeedStartOption = FeedStartOption.FeedVersion;
                        }
                        else
                        {
                            FeedStartTimeUtc = DateTime.UtcNow;
                            feedResult = await myGeotabAPIHelper.GetFeedAsync<T>(FeedStartTimeUtc, FeedResultsLimit, adapterConfiguration.TimeoutSecondsForMyGeotabTasks);

                            // Switch to FeedVersion for subsequent calls.
                            FeedStartOption = FeedStartOption.FeedVersion;
                        }
                        break;
                    case FeedStartOption.SpecificTime:
                        // GetFeed *search not supported for DeviceStatusInfo. 
                        if (typeParameterType == typeof(DeviceStatusInfo))
                        {
                            feedResult = await myGeotabAPIHelper.GetFeedAsync<T>(LastFeedVersion, FeedResultsLimit, adapterConfiguration.TimeoutSecondsForMyGeotabTasks);

                            // Switch to FeedVersion for subsequent calls.
                            FeedStartOption = FeedStartOption.FeedVersion;
                        }
                        else
                        {
                            feedResult = await myGeotabAPIHelper.GetFeedAsync<T>(FeedStartTimeUtc, FeedResultsLimit, adapterConfiguration.TimeoutSecondsForMyGeotabTasks);

                            // Switch to FeedVersion for subsequent calls.
                            FeedStartOption = FeedStartOption.FeedVersion;
                        }
                        break;
                    case FeedStartOption.FeedVersion:
                        feedResult = await myGeotabAPIHelper.GetFeedAsync<T>(LastFeedVersion, FeedResultsLimit, adapterConfiguration.TimeoutSecondsForMyGeotabTasks);
                        break;
                    default:
                        break;
                }

                cancellationToken.ThrowIfCancellationRequested();

                logger.Debug($"GetFeedAsync<{typeParameterType.Name}> returned with {feedResult.Data.Count} records.");
                LastFeedVersion = feedResult.ToVersion;

                // Add FeedResult data to the FeedContainer.
                foreach (IEntity feedResultItem in feedResult.Data)
                {
                    if (typeParameterType.Name == typeof(DeviceStatusInfo).Name)
                    {
                        var deviceStatusInfo = (DeviceStatusInfo)feedResultItem;
                        var device = deviceStatusInfo.Device;
                        deviceStatusInfo.Id = device.Id;

                        FeedResultData.AddOrUpdate(deviceStatusInfo.Id, (T)feedResultItem, (key, currentValue) => (T)feedResultItem);
                    }
                    else
                    {
                        FeedResultData.AddOrUpdate(feedResultItem.Id, (T)feedResultItem, (key, currentValue) => (T)feedResultItem);
                    }
                }

                // Determine whether the feed is up-to-date.
                if (feedResult.Data.Count < FeedCurrentThresholdRecordCount)
                {
                    FeedCurrent = true;
                }
                else
                {
                    FeedCurrent = false;
                }

                LastFeedRetrievalTimeUtc = DateTime.UtcNow;
                logger.Info($"{typeParameterType.Name} feed polled with {FeedResultData.Count} records returned.");
            }
            else
            {
                logger.Debug($"{typeParameterType.Name} data feed not polled; {FeedPollingIntervalSeconds} seconds have not passed since last poll.");
            }
        }

        /// <inheritdoc/>
        public List<T> GetFeedResultDataValuesList()
        {
            var feedResultDataValues = new List<T>();
            foreach (var item in FeedResultData.Values)
            {
                T value = (T)item;
                feedResultDataValues.Add(value);
            }
            return feedResultDataValues;
        }

        /// <inheritdoc/>
        public async Task InitializeAsync(CancellationTokenSource cancellationTokenSource, int feedPollingIntervalSeconds, int feedResultsLimit, long? lastFeedVersion)
        {
            await initializationLock.WaitAsync();
            try
            {
                if (IsInitialized == false)
                {
                    FeedPollingIntervalSeconds = feedPollingIntervalSeconds;
                    FeedResultsLimit = feedResultsLimit;
                    if (lastFeedVersion != null)
                    {
                        LastFeedVersion = lastFeedVersion;
                    }

                    // If the FeedStartOption is not set to FeedVersion, but the OServiceTracking record indicates that data has already been captured for the subject Geotab object type, switch the FeedStartOption to FeedVersion to avoid sending duplicate records to the database.
                    if (FeedStartOption != FeedStartOption.FeedVersion && LastFeedVersion > 0)
                    {
                        logger.Info($"Switching FeedStartOption for '{typeParameterType}' from '{FeedStartOption}' to '{FeedStartOption.FeedVersion}' to prevent data duplication because data has already been written to the associated database table(s).");
                        FeedStartOption = FeedStartOption.FeedVersion;
                    }

                    IsInitialized = true;
                }
                else
                {
                    logger.Debug($"The current {CurrentClassName} has already been initialized.");
                }
            }
            finally
            {
                initializationLock.Release();
            }
        }

        /// <inheritdoc/>
        public void Rollback(long? lastProcessedFeedVersion)
        { 
            LastFeedVersion = lastProcessedFeedVersion;
            LastFeedRetrievalTimeUtc = DateTime.MinValue;

            if (lastProcessedFeedVersion == null && FeedStartOption != ConfiguredFeedStartOption)
            {
                FeedStartOption = ConfiguredFeedStartOption;
            }
        }

        /// <summary>
        /// Checks whether the <see cref="InitializeAsync(CancellationTokenSource, int, int, long)"/> method has been invoked since the current class instance was created and throws an <see cref="InvalidOperationException"/> if initialization has not been performed.
        /// </summary>
        void ValidateInitialized()
        {
            if (IsInitialized == false)
            {
                throw new InvalidOperationException($"The current {CurrentClassName} has not been initialized. The {nameof(InitializeAsync)} method must be called before other methods can be invoked.");
            }
        }
    }
}
