@echo off
REM Quick Setup Script for MyGeotab Adapter System
REM One-command setup for new deployments or resets

echo ========================================
echo MyGeotab Adapter Quick Setup
echo ========================================
echo.

echo [CHECK] Checking prerequisites...

REM Check if Docker is running
docker --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Docker is not installed or not running
    echo Please install Docker Desktop and ensure it's running
    pause
    exit /b 1
)

REM Check if Node.js is available
node --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Node.js is not installed
    echo Please install Node.js and try again
    pause
    exit /b 1
)

echo [OK] Prerequisites verified

echo.
echo [START] Starting Quick Setup Process...
echo.

REM Step 1: Start Docker services
echo [STEP 1/5] Starting Docker services...
docker-compose up -d
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Failed to start Docker services
    pause
    exit /b 1
)
echo [OK] Docker services started

REM Step 2: Wait for services to be healthy
echo [STEP 2/5] Waiting for services to initialize (30 seconds)...
timeout /t 30 /nobreak >nul

REM Step 3: Setup database (if needed)
echo [STEP 3/5] Checking database setup...
node test-database-users.js >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [WARNING] Database users need initialization
    echo Running database setup...
    del .db_setup_success >nul 2>&1
    cmd /c setup-database-wrapper.bat
    if exist .db_setup_success (
        del .db_setup_success >nul 2>&1
    ) else (
        echo [ERROR] Database setup failed
        pause
        exit /b 1
    )
    echo [OK] Database setup completed
) else (
    echo [OK] Database users already configured
)

REM Step 4: Check MyGeotab configuration
echo [STEP 4/5] Checking MyGeotab configuration...
if exist "official-adapter\MyGeotabAPIAdapter_SCD_win-x64\appsettings.json" (
    REM Check if configuration has credentials
    findstr /C:"MyGeotabUser" "official-adapter\MyGeotabAPIAdapter_SCD_win-x64\appsettings.json" | findstr /V /C:"\"\"" >nul
    if %ERRORLEVEL% equ 0 (
        echo [OK] MyGeotab credentials configured
    ) else (
        echo [WARNING] MyGeotab credentials need configuration
        echo.
        echo Please run: npm run configure-mygeotab
        echo Then restart this setup with: npm run quick-setup
        pause
        exit /b 1
    )
) else (
    echo [ERROR] MyGeotab configuration file not found
    echo Please ensure the official adapter is properly installed
    pause
    exit /b 1
)

REM Step 5: Start the adapter
echo [STEP 5/5] Starting MyGeotab Adapter...
start-adapter-service.bat

echo.
echo [SUCCESS] Quick Setup Completed Successfully!
echo.
echo [STATUS] System Status:
npm run system-status

echo.
echo [ACCESS] Access URLs:
echo   - Grafana Dashboard: http://localhost:3000 (admin/admin123)
echo   - Database Monitor: http://localhost:8081 (admin/pghero123)
echo   - Container Logs: http://localhost:8080
echo.

REM Optional monitoring setup prompt
echo ========================================
echo OPTIONAL: Enterprise Monitoring Stack
echo ========================================
echo.
echo Would you like to install additional enterprise monitoring tools?
echo This includes:
echo   - Netdata (Infrastructure monitoring)
echo   - Uptime Kuma (Service uptime monitoring)
echo   - Loki + Promtail (Centralized logging)
echo   - Watchtower (Automated updates)
echo   - pgAdmin (Database administration)
echo.
set /p install_monitoring="Install enterprise monitoring? (y/n): "
if /i "%install_monitoring%"=="y" (
    echo.
    echo [MONITORING] Installing enterprise monitoring stack...
    call setup-monitoring.bat
    if %ERRORLEVEL% equ 0 (
        echo.
        echo [SUCCESS] Enterprise monitoring installed successfully!
        echo See monitoring-manual-setup.md for additional configuration steps.
    ) else (
        echo.
        echo [WARNING] Monitoring installation had issues. Check the output above.
    )
) else (
    echo.
    echo [INFO] Monitoring stack skipped. You can install it later with:
    echo        npm run setup-monitoring
)

echo.
echo [COMMANDS] Useful Commands:
echo   - Check status: npm run system-status
echo   - Monitor adapter: npm run monitor-adapter
echo   - Restart adapter: npm run restart-adapter
echo   - View logs: npm run logs
if /i "%install_monitoring%"=="y" (
    echo   - Monitoring status: npm run monitoring-status
    echo   - Monitoring logs: npm run monitoring-logs
)
echo.
pause
