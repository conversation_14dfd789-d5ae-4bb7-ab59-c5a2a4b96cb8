//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute("dotnet-MyGeotabAPIAdapter-647298B6-D50F-498C-A6D9-8910D78DFF6D")]
[assembly: System.Reflection.AssemblyCompanyAttribute("Geotab Inc.")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyDescriptionAttribute(@"A .NET Core (C#) Worker Service designed to use the MyGeotab .NET API and serve as a broker between a MyGeotab database and an associated ""Virtual Geotab Database"". Intended for use when direct utilization of the MyGeotab SDK is not an option. Modify as required to meet individual solution objectives.")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("*******+b0fba51ee465f39f66a3aaaa0c829cfa3a09785b")]
[assembly: System.Reflection.AssemblyProductAttribute("MyGeotab API Adapter")]
[assembly: System.Reflection.AssemblyTitleAttribute("MyGeotabAPIAdapter")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]

// Generated by the MSBuild WriteCodeFragment class.

