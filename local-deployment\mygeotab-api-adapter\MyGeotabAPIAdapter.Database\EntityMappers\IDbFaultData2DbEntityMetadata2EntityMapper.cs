﻿using MyGeotabAPIAdapter.Database.Models;


namespace MyGeotabAPIAdapter.Database.EntityMappers
{
    /// <summary>
    /// Interface for a class with methods involving mapping between <see cref="DbFaultData2"/> and <see cref="DbEntityMetadata2"/> entities.
    /// </summary>
    public interface IDbFaultData2DbEntityMetadata2EntityMapper : ICreateOnlyEntityMapper<DbFaultData2, DbEntityMetadata2>
    {
    }
}
