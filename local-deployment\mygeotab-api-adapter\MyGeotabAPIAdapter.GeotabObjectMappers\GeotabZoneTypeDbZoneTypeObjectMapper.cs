﻿using Geotab.Checkmate.ObjectModel;
using MyGeotabAPIAdapter.Database;
using MyGeotabAPIAdapter.Database.Models;
using MyGeotabAPIAdapter.Helpers;

namespace MyGeotabAPIAdapter.GeotabObjectMappers
{
    /// <summary>
    /// A class with methods involving mapping between <see cref="ZoneType"/> and <see cref="DbZoneType"/> entities.
    /// </summary>
    public class GeotabZoneTypeDbZoneTypeObjectMapper : IGeotabZoneTypeDbZoneTypeObjectMapper
    {
        readonly IStringHelper stringHelper;

        public GeotabZoneTypeDbZoneTypeObjectMapper(IStringHelper stringHelper)
        { 
            this.stringHelper = stringHelper;
        }

        /// <inheritdoc/>
        public DbZoneType CreateEntity(ZoneType entityToMapTo, Common.DatabaseRecordStatus entityStatus = Common.DatabaseRecordStatus.Active)
        {
            DbZoneType dbZoneType = new()
            {
                DatabaseWriteOperationType = Common.DatabaseWriteOperationType.Insert,
                EntityStatus = (int)entityStatus,
                GeotabId = entityToMapTo.Id.ToString(),
                Name = entityToMapTo.Name,
                RecordLastChangedUtc = DateTime.UtcNow
            };
            if (entityToMapTo.Comment != null && entityToMapTo.Comment.Length > 0)
            {
                dbZoneType.Comment = entityToMapTo.Comment;
            }
            return dbZoneType;
        }

        /// <inheritdoc/>
        public bool EntityRequiresUpdate(DbZoneType entityToEvaluate, ZoneType entityToMapTo)
        {
            if (entityToEvaluate.GeotabId != entityToMapTo.Id.ToString())
            {
                throw new ArgumentException($"Cannot compare {nameof(DbZoneType)} '{entityToEvaluate.id}' with {nameof(ZoneType)} '{entityToMapTo.Id}' because the IDs do not match.");
            }

            if (stringHelper.AreEqual(entityToEvaluate.Comment, entityToMapTo.Comment) == false)
            {
                return true;
            }
            if (stringHelper.AreEqual(entityToEvaluate.Name, entityToMapTo.Name) == false)
            {
                return true;
            }
            return false;
        }

        /// <inheritdoc/>
        public DbZoneType UpdateEntity(DbZoneType entityToUpdate, ZoneType entityToMapTo, Common.DatabaseRecordStatus entityStatus = Common.DatabaseRecordStatus.Active)
        {
            if (entityToUpdate.GeotabId != entityToMapTo.Id.ToString())
            {
                throw new ArgumentException($"Cannot update {nameof(DbZoneType)} '{entityToUpdate.id} (GeotabId {entityToUpdate.GeotabId})' with {nameof(ZoneType)} '{entityToMapTo.Id}' because the GeotabIds do not match.");
            }

            var updatedDbZoneType = CreateEntity(entityToMapTo);

            // Update id since id is auto-generated by the database on insert and is therefor not set by the CreateEntity method.
            updatedDbZoneType.id = entityToUpdate.id;
            updatedDbZoneType.DatabaseWriteOperationType = Common.DatabaseWriteOperationType.Update;

            return updatedDbZoneType;
        }
    }
}
