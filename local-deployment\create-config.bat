@echo off
REM Configuration file generator for MyGeotab Adapter
REM Usage: create-config.bat "server" "database" "username" "password"

if "%~4"=="" (
    echo Error: Missing parameters
    echo Usage: create-config.bat "server" "database" "username" "password"
    exit /b 1
)

set SERVER=%~1
set DATABASE=%~2
set USERNAME=%~3
set PASSWORD=%~4

echo Creating appsettings.json configuration file...

(
echo {
echo   "DatabaseSettings": {
echo     "DatabaseProviderType": "PostgreSQL",
echo     "ConnectionString": "Host=localhost;Port=5432;Database=geotabadapterdb;Username=geotabadapter_client;Password=localdev123;",
echo     "UncategorizedDatabaseWriteOperationTimeoutSeconds": 30,
echo     "DatabaseCommandTimeoutSeconds": 60,
echo     "DatabaseConnectionTestTimeoutSeconds": 20,
echo     "DataOptimizerEnabled": true,
echo     "DataOptimizerProcessingIntervalMinutes": 60
echo   },
echo   "AppSettings": {
echo     "DisableMachineNameValidation": true,
echo     "BypassAdapterDatabaseInitializationCheck": false,
echo     "EnableMinimumIntervalSamplingForLogRecords": false,
echo     "EnableMinimumIntervalSamplingForStatusData": false,
echo     "TrackZoneStops": true,
echo     "EnableDebugMode": false,
echo     "TimeoutSecondsForDatabaseTasks": 600,
echo     "TimeoutSecondsForMyGeotabTasks": 300,
echo     "MessageQueueingEnabled": false,
echo     "MessageQueueingBatchSize": 1000,
echo     "MessageQueueingIntervalMilliseconds": 1000
echo   },
echo   "LoginSettings": {
echo     "MyGeotabServer": "%SERVER%",
echo     "MyGeotabDatabase": "%DATABASE%",
echo     "MyGeotabUser": "%USERNAME%",
echo     "MyGeotabPassword": "%PASSWORD%"
echo   },
echo   "FeedSettings": {
echo     "ExceptionEventFeedSettings": {
echo       "FeedEnabled": true,
echo       "FeedIntervalSeconds": 30
echo     },
echo     "FaultDataFeedSettings": {
echo       "FeedEnabled": true,
echo       "FeedIntervalSeconds": 30
echo     },
echo     "LogRecordFeedSettings": {
echo       "FeedEnabled": true,
echo       "FeedIntervalSeconds": 30
echo     },
echo     "StatusDataFeedSettings": {
echo       "FeedEnabled": true,
echo       "FeedIntervalSeconds": 30
echo     },
echo     "TripFeedSettings": {
echo       "FeedEnabled": true,
echo       "FeedIntervalSeconds": 30
echo     },
echo     "BinaryDataFeedSettings": {
echo       "FeedEnabled": false,
echo       "FeedIntervalSeconds": 30
echo     },
echo     "ChargeEventFeedSettings": {
echo       "FeedEnabled": false,
echo       "FeedIntervalSeconds": 30
echo     },
echo     "DebugDataFeedSettings": {
echo       "FeedEnabled": false,
echo       "FeedIntervalSeconds": 30
echo     },
echo     "DeviceStatusInfoFeedSettings": {
echo       "FeedEnabled": false,
echo       "FeedIntervalSeconds": 30
echo     },
echo     "DriverChangeFeedSettings": {
echo       "FeedEnabled": false,
echo       "FeedIntervalSeconds": 30
echo     },
echo     "DutyStatusAvailabilityFeedSettings": {
echo       "FeedEnabled": false,
echo       "FeedIntervalSeconds": 30
echo     },
echo     "DVIRLogFeedSettings": {
echo       "FeedEnabled": false,
echo       "FeedIntervalSeconds": 30
echo     }
echo   }
echo }
) > appsettings.json

echo Configuration file created successfully.
