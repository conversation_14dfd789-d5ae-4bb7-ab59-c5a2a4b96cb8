﻿using System;
using System.Collections.Generic;
using System.Text;
using Geotab.Checkmate.ObjectModel;
using MyGeotabAPIAdapter.Database;
using MyGeotabAPIAdapter.Database.DataAccess;
using MyGeotabAPIAdapter.Database.Models;
using Xunit;

namespace MyGeotabAPIAdapter.Tests
{
    public class DbLogRecordRepository
    {
        //    //var configMock = new Mock<IConfiguration>();
        //    //configMock
        //    //    .Setup(x => x[keyString])
        //    //    .Returns("StringValue");
        //var mock = new Mock<DbLogRecordRepository>();
        //mock.Setup()
        //ConnectionInfo connectionInfo;
        //DbLogRecord dbLogRecord = DbLogRecordRepository().GetAsync(connectionInfo, Id);


    }
}
