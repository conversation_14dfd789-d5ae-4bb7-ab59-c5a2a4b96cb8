﻿<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <UserSecretsId>dotnet-MyGeotabAPIAdapter.DataOptimizer-869D4350-5E76-4BAE-AC1D-08B82AF48ECA</UserSecretsId>
    <Authors>Geotab Inc.</Authors>
    <Company>Geotab Inc.</Company>
    <Product>MyGeotab API Adapter - DataOptimizer</Product>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <RuntimeIdentifiers>win-x64;linux-x64</RuntimeIdentifiers>
    <Description>A worker service designed to migrate data from the MyGeotab API Adapter database into another set of tables that are optimized for use by applications and data analysis tools. Additional columns are added to some of the tables and these are populated via interpolation or other query-based procedues.</Description>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="LOG-MyGeotab_API_Adapter-Data_Optimizer-internal.log" />
  </ItemGroup> 
  
  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.5" />
    <PackageReference Include="Microsoft.NETCore.Platforms" Version="7.0.4" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NLog.Extensions.Logging" Version="5.5.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\MyGeotabAPIAdapter.Configuration\MyGeotabAPIAdapter.Configuration.csproj" />
    <ProjectReference Include="..\MyGeotabAPIAdapter.Database.EntityPersisters\MyGeotabAPIAdapter.Database.EntityPersisters.csproj" />
    <ProjectReference Include="..\MyGeotabAPIAdapter.Database\MyGeotabAPIAdapter.Database.csproj" />
    <ProjectReference Include="..\MyGeotabAPIAdapter.Geospatial\MyGeotabAPIAdapter.Geospatial.csproj" />
    <ProjectReference Include="..\MyGeotabAPIAdapter.Helpers\MyGeotabAPIAdapter.Helpers.csproj" />
    <ProjectReference Include="..\MyGeotabAPIAdapter.Logging\MyGeotabAPIAdapter.Logging.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Properties\PublishProfiles\" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

	<ItemGroup>
		<Content Remove="appsettings.Development.json" />
	</ItemGroup>

	<ItemGroup>
	  <_WebToolingArtifacts Remove="Properties\launchSettings.json" />
	</ItemGroup>

	<ItemGroup>
	  <Content Include="Properties\launchSettings.json" />
	</ItemGroup>

	<ItemGroup>
		<None Include="appsettings.Development.json" />
	</ItemGroup>

</Project>
