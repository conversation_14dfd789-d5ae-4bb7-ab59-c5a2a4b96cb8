# 🚀 MyGeotab Fleet Monitoring - Quick Start (Fully Automated)

Get your MyGeotab fleet monitoring system running in **under 5 minutes** with **complete automation** - no manual steps required!

## ⚡ Prerequisites

- **Windows environment** (Official adapter is Windows-specific)
- **Docker Desktop** installed and running
- **MyGeotab account** with API access (pre-configured with sample data)
- **Available ports**: 3000, 5432, 8080, 8081

## 🎯 One-Command Complete Setup

### 1. Clone and Navigate
```bash
git clone <repository-url>
cd mygeotab-api-adapter/local-deployment
```

### 2. Install Dependencies
```bash
npm install
```

### 3. **Complete Automated Setup**
```bash
npm run quick-setup
```

**This single command automatically:**
✅ Starts all Docker services (PostgreSQL, Grafana, PgHero, Dozzle)
✅ Waits for services to initialize (30 seconds)
✅ **Automatically detects and creates database users** when needed
✅ Sets up complete database schema with partitioning
✅ Verifies MyGeotab configuration exists
✅ Starts the MyGeotab Adapter automatically
✅ Provides complete system status and access URLs

**No manual steps required!** The system is production-ready after this single command.

## ✅ Access Your System

**Check these URLs:**

| Service | URL | Credentials | Purpose |
|---------|-----|-------------|---------|
| **Grafana** | http://localhost:3000 | admin/admin123 | Fleet dashboards |
| **PgHero** | http://localhost:8081 | admin/pghero123 | Database monitoring |
| **Dozzle** | http://localhost:8080 | - | Container logs |

## 🔧 Configuration (Optional)

The system comes pre-configured with sample MyGeotab credentials. To use your own:

```bash
npm run configure-mygeotab
```

## 📊 What You Get After Automation

### **Real-time Fleet Data**
- Vehicle locations and status
- Trip data and analytics
- Driver performance metrics
- Fault codes and diagnostics
- Engine health monitoring

### **Official Database Schema**
- **LogRecords2** - GPS tracking data
- **StatusData2** - Engine diagnostics
- **FaultData2** - Fault codes
- **Trips2** - Trip information
- **Devices2** - Vehicle information
- **Users2** - Driver information
- **Plus 20+ additional tables**

### **Monitoring Stack**
- Real-time Grafana dashboards
- PostgreSQL performance monitoring
- Container log management
- Redis caching for performance

## 🔍 Quick Verification

### Check Database Data
```bash
# Connect to database
docker exec postgres psql -U pgadmin -d monitoring_db

# Check vehicle count
SELECT COUNT(*) FROM "Devices2";

# Check latest GPS data
SELECT "DeviceId", "DateTime", "Latitude", "Longitude", "Speed"
FROM "LogRecords2"
ORDER BY "DateTime" DESC LIMIT 5;
```

### Check Services
```bash
# View all container status
docker-compose ps

# View logs
docker-compose logs -f
```

## 🛠️ Common Commands

```bash
# Stop all services
docker-compose down

# Reset everything (delete data)
docker-compose down -v
docker-compose up -d

# Reconfigure MyGeotab
npm run configure-mygeotab

# Test adapter connection
npm run test-adapter
```

## 🔧 Troubleshooting

### **No Data in Database?**
1. Wait 2-3 minutes for first sync
2. Check adapter is running and connected
3. Verify MyGeotab credentials are correct

### **Grafana Can't Connect?**
1. Use hostname `postgres` not `localhost`
2. Check credentials: pgadmin/localdev123
3. Verify PostgreSQL is running

### **MyGeotab Connection Issues?**
1. Check credentials at configuration wizard
2. Verify MyGeotab server URL
3. Ensure account has API access
4. Check Windows firewall settings

### **Reset Everything**
```bash
# Complete reset
docker-compose down -v
docker-compose up -d
npm run configure-mygeotab
npm run start-adapter
```

## 🎉 You're Ready!

Your MyGeotab fleet monitoring system is now running with:
- ✅ Official Geotab API Adapter
- ✅ Real-time data synchronization
- ✅ Complete database schema
- ✅ Grafana dashboards
- ✅ Performance monitoring

**Next Steps:**
1. Create custom Grafana dashboards for your fleet
2. Set up alerts for critical events
3. Explore the official database schema
4. Build custom reports and analytics

---

**🚛 Happy fleet monitoring!**

For detailed setup information, see [SETUP.md](SETUP.md)
