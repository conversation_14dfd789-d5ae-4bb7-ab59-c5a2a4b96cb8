﻿<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Configurations>Debug;Release</Configurations>
    <UserSecretsId>dotnet-MyGeotabAPIAdapter-647298B6-D50F-498C-A6D9-8910D78DFF6D</UserSecretsId>
    <RootNamespace>MyGeotabAPIAdapter</RootNamespace>
    <Authors>Geotab Inc.</Authors>
    <Product>MyGeotab API Adapter</Product>
    <Description>A .NET Core (C#) Worker Service designed to use the MyGeotab .NET API and serve as a broker between a MyGeotab database and an associated "Virtual Geotab Database". Intended for use when direct utilization of the MyGeotab SDK is not an option. Modify as required to meet individual solution objectives.</Description>
    <Copyright></Copyright>
    <Version>*******</Version>
    <InformationalVersion>*******</InformationalVersion>
    <RuntimeIdentifiers>win-x64;linux-x64</RuntimeIdentifiers>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="LOG-MyGeotabAPIAdapter-internal.log" />
    <None Remove="Program - Copy.cs.bak" />
  </ItemGroup>

  <ItemGroup>
    <None Include="..\.editorconfig" Link=".editorconfig" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Geotab.Checkmate.ObjectModel" Version="11.68.266" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.5" />
    <PackageReference Include="Microsoft.NETCore.Platforms" Version="7.0.4" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NLog.Extensions.Logging" Version="5.5.0" />
    <PackageReference Include="Oracle.ManagedDataAccess.Core" Version="23.8.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\MyGeotabAPIAdapter.Configuration\MyGeotabAPIAdapter.Configuration.csproj" />
    <ProjectReference Include="..\MyGeotabAPIAdapter.Database.EntityPersisters\MyGeotabAPIAdapter.Database.EntityPersisters.csproj" />
    <ProjectReference Include="..\MyGeotabAPIAdapter.Database\MyGeotabAPIAdapter.Database.csproj" />
    <ProjectReference Include="..\MyGeotabAPIAdapter.Geospatial\MyGeotabAPIAdapter.Geospatial.csproj" />
    <ProjectReference Include="..\MyGeotabAPIAdapter.GeotabObjectMappers\MyGeotabAPIAdapter.GeotabObjectMappers.csproj" />
    <ProjectReference Include="..\MyGeotabAPIAdapter.Helpers\MyGeotabAPIAdapter.Helpers.csproj" />
    <ProjectReference Include="..\MyGeotabAPIAdapter.Logging\MyGeotabAPIAdapter.Logging.csproj" />
    <ProjectReference Include="..\MyGeotabAPIAdapter.MyGeotabAPI\MyGeotabAPIAdapter.MyGeotabAPI.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>    
  </ItemGroup>

  <ItemGroup>
	<Content Remove="appsettings.Development.json" />
  </ItemGroup>

  <ItemGroup>
	<None Include="appsettings.Development.json" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Scripts\PostgreSQL\v2\" />
  </ItemGroup>	
	
</Project>
