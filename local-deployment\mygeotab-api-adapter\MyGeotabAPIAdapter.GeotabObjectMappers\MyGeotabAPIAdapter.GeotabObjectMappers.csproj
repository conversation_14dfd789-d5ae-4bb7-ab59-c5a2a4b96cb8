﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Geotab.Checkmate.ObjectModel" Version="11.68.266" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NLog.Extensions.Logging" Version="5.5.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\MyGeotabAPIAdapter.Database\MyGeotabAPIAdapter.Database.csproj" />
    <ProjectReference Include="..\MyGeotabAPIAdapter.Helpers\MyGeotabAPIAdapter.Helpers.csproj" />
    <ProjectReference Include="..\MyGeotabAPIAdapter.MyGeotabAPI\MyGeotabAPIAdapter.MyGeotabAPI.csproj" />
  </ItemGroup>

</Project>
