@echo off
REM Performance Monitor for MyGeotab Adapter System
REM Provides detailed performance metrics and system health

echo ========================================
echo MyGeotab Adapter Performance Monitor
echo ========================================
echo.

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs

REM Set log file with timestamp
for /f "tokens=1-3 delims=/ " %%a in ('date /t') do set "datestamp=%%c-%%a-%%b"
for /f "tokens=1-2 delims=: " %%a in ('time /t') do set "timestamp=%%a-%%b"

echo [PERFORMANCE] Performance Report - %date% %time%
echo ========================================

echo.
echo [ADAPTER] MyGeotab Adapter Performance:
echo ----------------------------------------
tasklist /FI "IMAGENAME eq MyGeotabAPIAdapter.exe" 2>NUL | find /I /N "MyGeotabAPIAdapter.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [OK] Adapter Status: RUNNING

    REM Get detailed process metrics
    powershell -Command "Get-Process -Name MyGeotabAPIAdapter | Select-Object Name, Id, CPU, WorkingSet, VirtualMemorySize, StartTime | Format-Table -AutoSize"

    REM Get process uptime
    powershell -Command "$process = Get-Process -Name MyGeotabAPIAdapter; $uptime = (Get-Date) - $process.StartTime; Write-Host 'Uptime:' $uptime.Days 'days' $uptime.Hours 'hours' $uptime.Minutes 'minutes'"

) else (
    echo [ERROR] Adapter Status: NOT RUNNING
)

echo.
echo [DOCKER] Docker Services Performance:
echo ----------------------------------------
docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}"

echo.
echo [DATABASE] Database Performance:
echo ----------------------------------------
echo Testing database connectivity and performance...

REM Time the database test
powershell -Command "$start = Get-Date; $null = node test-adapter.js 2>$null; $end = Get-Date; $duration = ($end - $start).TotalMilliseconds; Write-Host 'Database Response Time:' ([math]::Round($duration, 2)) 'ms'"

echo.
echo [DATA] Data Growth Analysis:
echo ----------------------------------------
node test-adapter.js 2>nul | findstr "records"

echo.
echo [SYSTEM] System Resources:
echo ----------------------------------------
echo CPU Usage:
powershell -Command "Get-WmiObject win32_processor | Measure-Object -property LoadPercentage -Average | Select-Object -ExpandProperty Average | ForEach-Object { Write-Host $_ '%%' }"

echo.
echo Memory Usage:
powershell -Command "Get-WmiObject -Class Win32_OperatingSystem | Select-Object @{Name='MemoryUsage';Expression={'{0:N2}' -f ((($_.TotalVisibleMemorySize - $_.FreePhysicalMemory)*100)/ $_.TotalVisibleMemorySize) + '%'}} | Format-Table -HideTableHeaders"

echo.
echo [NETWORK] Network Connectivity:
echo ----------------------------------------
echo Testing MyGeotab API connectivity...
powershell -Command "$ProgressPreference = 'SilentlyContinue'; try { $response = Invoke-WebRequest -Uri 'https://my.geotab.com' -UseBasicParsing -TimeoutSec 5; Write-Host 'MyGeotab API: [OK] Reachable (' $response.StatusCode ')' } catch { Write-Host 'MyGeotab API: [ERROR] Not reachable' }"

echo Testing local services...
powershell -Command "$ProgressPreference = 'SilentlyContinue'; try { Invoke-WebRequest -Uri 'http://localhost:3000' -UseBasicParsing -TimeoutSec 3 | Out-Null; Write-Host 'Grafana: [OK] Accessible' } catch { Write-Host 'Grafana: [ERROR] Not accessible' }"
powershell -Command "$ProgressPreference = 'SilentlyContinue'; try { Invoke-WebRequest -Uri 'http://localhost:8080' -UseBasicParsing -TimeoutSec 3 | Out-Null; Write-Host 'Dozzle: [OK] Accessible' } catch { Write-Host 'Dozzle: [ERROR] Not accessible' }"

echo.
echo [SUMMARY] Performance Summary:
echo ----------------------------------------
echo Report generated: %date% %time%
echo.

REM Save performance data to log file
echo Performance Report - %date% %time% > "logs\performance-%datestamp%.log"
echo ================================================ >> "logs\performance-%datestamp%.log"
tasklist /FI "IMAGENAME eq MyGeotabAPIAdapter.exe" >> "logs\performance-%datestamp%.log" 2>&1
docker stats --no-stream >> "logs\performance-%datestamp%.log" 2>&1

echo [TIPS] Tips for Optimization:
echo ----------------------------------------
echo 1. Monitor adapter memory usage - restart if it exceeds 1GB
echo 2. Check database growth - consider archiving old data
echo 3. Monitor Docker container resource usage
echo 4. Ensure adequate disk space for logs and data
echo.

echo [LOG] Performance log saved to: logs\performance-%datestamp%.log
echo.
pause
