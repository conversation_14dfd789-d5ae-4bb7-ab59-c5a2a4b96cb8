@echo off
REM Wrapper script for database setup that properly handles success/failure

echo Running database setup...
call setup-postgres-users.bat

REM Check if database users are working (this is the real test)
node test-database-users.js >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo [OK] Database setup completed successfully!
    echo SUCCESS > .db_setup_success
    exit /b 0
) else (
    echo [ERROR] Database setup failed - users not working
    exit /b 1
)
