@echo off
setlocal enabledelayedexpansion

echo ========================================
echo MyGeotab Adapter - Enterprise Monitoring Setup
echo ========================================
echo.

:: Check if Dock<PERSON> is running
echo [1/6] Checking Docker status...
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker Desktop and try again.
    pause
    exit /b 1
)
echo ✅ Docker is running

:: Check if main stack is running
echo.
echo [2/6] Checking main stack status...
docker ps --filter "name=postgres" --filter "status=running" | findstr postgres >nul
if errorlevel 1 (
    echo ❌ Main stack (PostgreSQL) is not running. Please start the main stack first:
    echo    npm run up
    pause
    exit /b 1
)
echo ✅ Main stack is running

:: Deploy monitoring stack
echo.
echo [3/6] Deploying monitoring stack...
docker-compose -f docker-compose.monitoring.yml up -d
if errorlevel 1 (
    echo ❌ Failed to deploy monitoring stack
    pause
    exit /b 1
)
echo ✅ Monitoring stack deployed

:: Wait for services to be healthy
echo.
echo [4/6] Waiting for services to start (this may take 60-90 seconds)...
timeout /t 30 /nobreak >nul

:: Check service health
echo.
echo [5/6] Checking service health...

set "all_healthy=true"

:: Check Netdata
echo Checking Netdata...
curl -f http://localhost:19999/api/v1/info >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Netdata not ready yet
    set "all_healthy=false"
) else (
    echo ✅ Netdata is healthy
)

:: Check Uptime Kuma
echo Checking Uptime Kuma...
curl -f http://localhost:3001 >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Uptime Kuma not ready yet
    set "all_healthy=false"
) else (
    echo ✅ Uptime Kuma is healthy
)

:: Check Loki
echo Checking Loki...
curl -f http://localhost:3100/ready >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Loki not ready yet
    set "all_healthy=false"
) else (
    echo ✅ Loki is healthy
)

:: Check pgAdmin
echo Checking pgAdmin...
curl -f http://localhost:5050/misc/ping >nul 2>&1
if errorlevel 1 (
    echo ⚠️  pgAdmin not ready yet
    set "all_healthy=false"
) else (
    echo ✅ pgAdmin is healthy
)

if "!all_healthy!"=="false" (
    echo.
    echo ⚠️  Some services are still starting. This is normal.
    echo    Services will be available shortly. Check status with: npm run monitoring-status
)

:: Add Loki datasource to Grafana
echo.
echo [6/6] Configuring Grafana integration...
timeout /t 10 /nobreak >nul

:: Copy Loki datasource configuration to Grafana
if not exist "grafana\provisioning\datasources" mkdir grafana\provisioning\datasources
copy "monitoring\grafana-provisioning\datasources\loki.yml" "grafana\provisioning\datasources\" >nul 2>&1

:: Restart Grafana to pick up new datasource
echo Restarting Grafana to load Loki datasource...
docker restart grafana >nul 2>&1

echo.
echo ========================================
echo ✅ MONITORING STACK SETUP COMPLETE!
echo ========================================
echo.
echo 📊 MONITORING SERVICES:
echo ├─ Netdata (Infrastructure):     http://localhost:19999
echo ├─ Uptime Kuma (Service Monitor): http://localhost:3001
echo ├─ pgAdmin (Database Admin):      http://localhost:5050
echo ├─ Grafana (with Loki logs):      http://localhost:3000
echo └─ Loki API (logs):               http://localhost:3100
echo.
echo 🔐 CREDENTIALS:
echo ├─ Uptime Kuma:  Create your own on first visit
echo ├─ pgAdmin:      <EMAIL> / monitoring123
echo ├─ Grafana:      admin / admin123
echo └─ Netdata:      No authentication required
echo.
echo 📋 NEXT STEPS:
echo 1. Configure Uptime Kuma monitors (see monitoring-manual-setup.md)
echo 2. Set up pgAdmin database connections
echo 3. Explore logs in Grafana using the new Loki datasource
echo 4. Configure notification channels (optional)
echo.
echo 🔧 MANAGEMENT COMMANDS:
echo ├─ Check status:     npm run monitoring-status
echo ├─ View logs:        npm run monitoring-logs  
echo ├─ Stop monitoring:  npm run monitoring-stop
echo └─ Restart:          npm run monitoring-restart
echo.

if "!all_healthy!"=="false" (
    echo ⚠️  Note: Some services may still be starting. Wait 1-2 minutes and check:
    echo    npm run monitoring-status
    echo.
)

echo Press any key to continue...
pause >nul
