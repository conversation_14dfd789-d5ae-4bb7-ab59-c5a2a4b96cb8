@echo off
REM First-Time Client Setup for MyGeotab Adapter
REM Complete guided setup experience for new customers

echo ========================================
echo MyGeotab Adapter - First Time Setup
echo ========================================
echo.
echo Welcome to MyGeotab Adapter!
echo This setup will guide you through the complete installation process.
echo.
echo What this setup will do:
echo   1. Clean any existing installation
echo   2. Prompt for your MyGeotab credentials
echo   3. Start all required services (PostgreSQL, Grafana)
echo   4. Initialize the database with proper schema
echo   5. Configure and start the MyGeotab Adapter
echo   6. Verify everything is working correctly
echo.
echo Prerequisites:
echo   - Docker Desktop must be installed and running
echo   - You need your MyGeotab account credentials
echo.
set /p continue="Ready to begin? (Y/N): "
if /i not "%continue%"=="Y" (
    echo Setup cancelled.
    pause
    exit /b 1
)

echo.
echo ========================================
echo STEP 1: Complete System Cleanup
echo ========================================
echo Removing any existing installation...

REM Stop any running services
echo [INFO] Stopping existing services...
docker-compose down -v --remove-orphans >nul 2>&1
taskkill /IM "MyGeotabAPIAdapter.exe" /F >nul 2>&1

REM Remove configuration files
echo [INFO] Removing old configuration files...
if exist appsettings.json del appsettings.json >nul 2>&1
if exist .db_setup_success del .db_setup_success >nul 2>&1

echo [OK] System cleanup completed
echo.

echo ========================================
echo STEP 2: MyGeotab Credentials Setup
echo ========================================
echo.
echo Please enter your MyGeotab account credentials.
echo These will be used to connect to your MyGeotab database.
echo.

:get_credentials
set /p mygeotab_server="MyGeotab Server (e.g., my.geotab.com): "
if "%mygeotab_server%"=="" (
    echo Error: Server cannot be empty
    goto get_credentials
)

set /p mygeotab_database="MyGeotab Database Name: "
if "%mygeotab_database%"=="" (
    echo Error: Database name cannot be empty
    goto get_credentials
)

set /p mygeotab_username="MyGeotab Username: "
if "%mygeotab_username%"=="" (
    echo Error: Username cannot be empty
    goto get_credentials
)

set /p mygeotab_password="MyGeotab Password: "
if "%mygeotab_password%"=="" (
    echo Error: Password cannot be empty
    goto get_credentials
)

echo.
echo [INFO] Credentials entered:
echo   Server: %mygeotab_server%
echo   Database: %mygeotab_database%
echo   Username: %mygeotab_username%
echo   Password: [HIDDEN]
echo.
set /p confirm="Are these credentials correct? (Y/N): "
if /i not "%confirm%"=="Y" goto get_credentials

echo [OK] Credentials configured
echo.

echo ========================================
echo STEP 3: Creating Configuration File
echo ========================================

echo [INFO] Generating appsettings.json...
call create-config.bat "%mygeotab_server%" "%mygeotab_database%" "%mygeotab_username%" "%mygeotab_password%"

if not exist appsettings.json (
    echo [ERROR] Failed to create configuration file
    pause
    exit /b 1
)

echo [OK] Configuration file created
echo.

echo ========================================
echo STEP 4: Starting Complete Setup
echo ========================================
echo.
echo This will now:
echo   - Start Docker services (PostgreSQL, Grafana, monitoring tools)
echo   - Initialize the database with proper schema and users
echo   - Start the MyGeotab Adapter
echo   - Verify all connections are working
echo.
echo This process takes approximately 3-5 minutes...
echo.

call quick-setup.bat

if %ERRORLEVEL% neq 0 (
    echo.
    echo [ERROR] Setup failed. Please check the error messages above.
    echo.
    echo Common solutions:
    echo   1. Verify Docker Desktop is running
    echo   2. Check your MyGeotab credentials are correct
    echo   3. Ensure you have internet connectivity
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo STEP 5: Final Verification
echo ========================================
echo.
echo Running final system verification...
echo.

call system-status.bat

echo.
echo ========================================
echo SETUP COMPLETE!
echo ========================================
echo.
echo Your MyGeotab Adapter is now fully configured and running!
echo.
echo Access your dashboards:
echo   - Grafana Dashboard: http://localhost:3000
echo     (Username: admin, Password: admin123)
echo   - Database Monitor: http://localhost:8081
echo     (Username: admin, Password: pghero123)
echo   - Container Logs: http://localhost:8080
echo.
echo Management Commands:
echo   - View system status: npm run system-status
echo   - Stop the adapter: npm run stop-adapter
echo   - View logs: npm run local-logs
echo   - Restart services: npm run local-deploy
echo.
echo The adapter is now synchronizing data from your MyGeotab account.
echo You can monitor progress in Grafana or check the logs.
echo.
echo Thank you for choosing MyGeotab Adapter!
echo.
pause
