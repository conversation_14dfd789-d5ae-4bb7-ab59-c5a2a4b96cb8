{
  "OverrideSetings": {
    "DisableMachineNameValidation": false
  },
  "DatabaseSettings": {
    "UseDataModel2": false,
    "EnableLevel1DatabaseMaintenance": true,
    "Level1DatabaseMaintenanceIntervalMinutes": 30,
    "EnableLevel2DatabaseMaintenance": true,
    "Level2DatabaseMaintenanceIntervalMinutes": 1440,
    "EnableLevel2DatabaseMaintenanceWindow": false,
    "Level2DatabaseMaintenanceWindowStartTimeUTC": "2020-06-23T06:00:00Z",
    "Level2DatabaseMaintenanceWindowMaxMinutes": 60,
    //"DatabaseProviderType": "PostgreSQL",
    //"DatabaseConnectionString": "Server=<Server>;Port=<Port>;Database=geotabadapterdb;User Id=geotabadapter_client;Password=<password>"
    "DatabaseProviderType": "SQLServer",
    "DatabaseConnectionString": "Server=<Server>;Database=geotabadapterdb;User Id=geotabadapter_client;Password=<password>;MultipleActiveResultSets=True;TrustServerCertificate=True"
    //"DatabaseProviderType": "Oracle",
    //"DatabaseConnectionString": "Data Source=<DataSource>;User Id=GeotabAdapter_Client;Password=<password>"
  },
  "LoginSettings": {
    "MyGeotabServer": "<MyGeotabServer>",
    "MyGeotabDatabase": "<MyGeotabDatabase>",
    "MyGeotabUser": "<MyGeotabUser>",
    "MyGeotabPassword": "<MyGeotabPassword>"
  },
  "AppSettings": {
    "GeneralSettings": {
      "TimeoutSecondsForDatabaseTasks": 30,
      "TimeoutSecondsForMyGeotabTasks": 30
    },
    "Caches": {
      "Controller": {
        "EnableControllerCache": true,
        "ControllerCacheIntervalDailyReferenceStartTimeUTC": "2020-06-23T06:00:00Z",
        "ControllerCacheUpdateIntervalMinutes": 1440,
        "ControllerCacheRefreshIntervalMinutes": 10080
      },
      "Device": {
        "EnableDeviceCache": true,
        "DeviceCacheIntervalDailyReferenceStartTimeUTC": "2020-06-23T06:00:00Z",
        "DeviceCacheUpdateIntervalMinutes": 10,
        "DeviceCacheRefreshIntervalMinutes": 1440
      },
      "Diagnostic": {
        "EnableDiagnosticCache": true,
        "DiagnosticCacheIntervalDailyReferenceStartTimeUTC": "2020-06-23T06:00:00Z",
        "DiagnosticCacheUpdateIntervalMinutes": 720,
        "DiagnosticCacheRefreshIntervalMinutes": 1440
      },
      "DVIRDefect": {
        "EnableDVIRDefectCache": true,
        "DVIRDefectListCacheIntervalDailyReferenceStartTimeUTC": "2020-06-23T06:00:00Z",
        "DVIRDefectListCacheRefreshIntervalMinutes": 1440
      },
      "FailureMode": {
        "EnableFailureModeCache": true,
        "FailureModeCacheIntervalDailyReferenceStartTimeUTC": "2020-06-23T06:00:00Z",
        "FailureModeCacheUpdateIntervalMinutes": 1440,
        "FailureModeCacheRefreshIntervalMinutes": 10080
      },
      "Group": {
        "EnableGroupCache": true,
        "GroupCacheIntervalDailyReferenceStartTimeUTC": "2020-06-23T06:00:00Z",
        "GroupCacheUpdateIntervalMinutes": 720,
        "GroupCacheRefreshIntervalMinutes": 1440
      },
      "Rule": {
        "EnableRuleCache": true,
        "RuleCacheIntervalDailyReferenceStartTimeUTC": "2020-06-23T06:00:00Z",
        "RuleCacheUpdateIntervalMinutes": 720,
        "RuleCacheRefreshIntervalMinutes": 1440
      },
      "UnitOfMeasure": {
        "EnableUnitOfMeasureCache": true,
        "UnitOfMeasureCacheIntervalDailyReferenceStartTimeUTC": "2020-06-23T06:00:00Z",
        "UnitOfMeasureCacheUpdateIntervalMinutes": 1440,
        "UnitOfMeasureCacheRefreshIntervalMinutes": 10080
      },
      "User": {
        "EnableUserCache": true,
        "UserCacheIntervalDailyReferenceStartTimeUTC": "2020-06-23T06:00:00Z",
        "UserCacheUpdateIntervalMinutes": 10,
        "UserCacheRefreshIntervalMinutes": 1440
      },
      "Zone": {
        "EnableZoneCache": true,
        "ZoneCacheIntervalDailyReferenceStartTimeUTC": "2020-06-23T06:00:00Z",
        "ZoneCacheUpdateIntervalMinutes": 60,
        "ZoneCacheRefreshIntervalMinutes": 1440
      },
      "ZoneType": {
        "EnableZoneTypeCache": true,
        "ZoneTypeCacheIntervalDailyReferenceStartTimeUTC": "2020-06-23T06:00:00Z",
        "ZoneTypeCacheUpdateIntervalMinutes": 1440,
        "ZoneTypeCacheRefreshIntervalMinutes": 10080
      }
    },
    "GeneralFeedSettings": {
      "FeedStartOption": "CurrentTime",
      "FeedStartSpecificTimeUTC": "2020-06-23T06:00:00Z",
      "DevicesToTrack": "*",
      "DiagnosticsToTrack": "*",
      "ExcludeDiagnosticsToTrack": false,
      "EnableMinimunIntervalSamplingForLogRecords": false,
      "EnableMinimunIntervalSamplingForStatusData": false,
      "MinimumIntervalSamplingDiagnostics": "*",
      "MinimumIntervalSamplingIntervalSeconds": 300
    },
    "Feeds": {
      "BinaryData": {
        "EnableBinaryDataFeed": true,
        "BinaryDataFeedIntervalSeconds": 30
      },
      "ChargeEvent": {
        "EnableChargeEventFeed": true,
        "ChargeEventFeedIntervalSeconds": 30
      },
      "DebugData": {
        "EnableDebugDataFeed": true,
        "DebugDataFeedIntervalSeconds": 30
      },
      "DeviceStatusInfo": {
        "EnableDeviceStatusInfoFeed": true,
        "DeviceStatusInfoFeedIntervalSeconds": 30
      },
      "DriverChange": {
        "EnableDriverChangeFeed": true,
        "DriverChangeFeedIntervalSeconds": 30
      },
      "DutyStatusAvailability": {
        "EnableDutyStatusAvailabilityFeed": false,
        "DutyStatusAvailabilityFeedIntervalSeconds": 300,
        "DutyStatusAvailabilityFeedLastAccessDateCutoffDays": 30
      },
      "DutyStatusLog": {
        "EnableDutyStatusLogFeed": true,
        "DutyStatusLogFeedIntervalSeconds": 30
      },
      "DVIRLog": {
        "EnableDVIRLogFeed": true,
        "DVIRLogFeedIntervalSeconds": 30
      },
      "ExceptionEvent": {
        "EnableExceptionEventFeed": true,
        "ExceptionEventFeedIntervalSeconds": 30,
        "TrackZoneStops": true
      },
      "FaultData": {
        "EnableFaultDataFeed": true,
        "FaultDataFeedIntervalSeconds": 30
      },
      "LogRecord": {
        "EnableLogRecordFeed": true,
        "LogRecordFeedIntervalSeconds": 30
      },
      "StatusData": {
        "EnableStatusDataFeed": true,
        "StatusDataFeedIntervalSeconds": 30
      },
      "Trip": {
        "EnableTripFeed": true,
        "TripFeedIntervalSeconds": 30
      }
    },
    "DataEnhancementServices": {
      "FaultData": {
        "EnableFaultDataLocationService": true,
        "FaultDataLocationServiceOperationMode": "Continuous",
        "FaultDataLocationServiceDailyStartTimeUTC": "2020-06-23T06:00:00Z",
        "FaultDataLocationServiceDailyRunTimeSeconds": 21600,
        "FaultDataLocationServiceExecutionIntervalSeconds": 60,
        "FaultDataLocationServicePopulateSpeed": true,
        "FaultDataLocationServicePopulateBearing": true,
        "FaultDataLocationServicePopulateDirection": true,
        "FaultDataLocationServiceNumberOfCompassDirections": 16,
        "FaultDataLocationServiceMaxDaysPerBatch": 2,
        "FaultDataLocationServiceMaxBatchSize": 100000,
        "FaultDataLocationServiceBufferMinutes": 1440
      },
      "StatusData": {
        "EnableStatusDataLocationService": true,
        "StatusDataLocationServiceOperationMode": "Continuous",
        "StatusDataLocationServiceDailyStartTimeUTC": "2020-06-23T06:00:00Z",
        "StatusDataLocationServiceDailyRunTimeSeconds": 21600,
        "StatusDataLocationServiceExecutionIntervalSeconds": 60,
        "StatusDataLocationServicePopulateSpeed": true,
        "StatusDataLocationServicePopulateBearing": true,
        "StatusDataLocationServicePopulateDirection": true,
        "StatusDataLocationServiceNumberOfCompassDirections": 16,
        "StatusDataLocationServiceMaxDaysPerBatch": 2,
        "StatusDataLocationServiceMaxBatchSize": 100000,
        "StatusDataLocationServiceBufferMinutes": 1440
      }
    },
    "Manipulators": {
      "DVIRLog": {
        "EnableDVIRLogManipulator": false,
        "DVIRLogManipulatorIntervalSeconds": 2
      }
    },
    "AddOns": {
      "VSS": {
        "EnableVSSAddOn": false,
        "DisableCorrespondingAdapterOutput": false,
        "OutputLogRecordsToOVDS": true,
        "OutputStatusDataToOVDS": true,
        "LogUnmappedDiagnostics": false,
        "UnmappedDiagnosticsLogIntervalMinutes": 5,
        "VSSPathMapFileURL": "https://storage.googleapis.com/geotab-vehicle_signal_specification-vsspathmaps/VSSPathMaps.json",
        "VSSVersion": "2.3",
        "VSSPathMapUpdateIntervalMinutes": 1440,
        "SendAttributeTypeDataToOVDS": true,
        "OVDSClientWorkerIntervalSeconds": 30,
        "OVDSServerURL": "http://*************:8765/ovdsserver",
        "OVDSSetCommandTemplate": "{\"action\":\"set\",\"vin\":\"PLACEHOLDER_VIN\",\"path\":\"PLACEHOLDER_PATH\",\"value\":\"PLACEHOLDER_VALUE\",\"timestamp\":\"PLACEHOLDER_TIMESTAMP\"}",
        "OVDSSetCommandTemplateForAttributeVSSPathMaps": "{\"action\":\"set\",\"vin\":\"PLACEHOLDER_VIN\",\"path\":\"PLACEHOLDER_PATH\",\"value\":\"PLACEHOLDER_VALUE\"}"
      }
    }
  }
}
