-- PostgreSQL Database Setup for MyGeotab Adapter
-- Creates separate users for data input and output flows

-- Create users if they don't exist
DO
$$
BEGIN
   -- User for MyGeotab Adapter to write data (data input)
   IF NOT EXISTS (SELECT FROM pg_roles WHERE rolname = 'geotabadapter_client') THEN
      CREATE USER geotabadapter_client WITH PASSWORD 'localdev123';
      RAISE NOTICE 'Created user: geotabadapter_client';
   END IF;

   -- User for <PERSON><PERSON> to read data (data output)
   IF NOT EXISTS (SELECT FROM pg_roles WHERE rolname = 'geotabadapter_reader') THEN
      CREATE USER geotabadapter_reader WITH PASSWORD 'localdev123';
      RAISE NOTICE 'Created user: geotabadapter_reader';
   END IF;
END
$$;

-- Grant database connection privileges
GRANT CONNECT ON DATABASE geotabadapterdb TO geotabadapter_client, geotabadapter_reader;

-- Connect to the geotabadapterdb database
\c geotabadapterdb

-- Grant schema usage
GRANT USAGE ON SCHEMA public TO geotabadapter_client, geotabadapter_reader;

-- Writer permissions (MyGeotab Adapter - data input)
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO geotabadapter_client;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO geotabadapter_client;
GRANT CREATE ON SCHEMA public TO geotabadapter_client;

-- Reader permissions (Grafana - data output)
-- Grafana needs to create its own tables for dashboards, users, etc.
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO geotabadapter_reader;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO geotabadapter_reader;
GRANT CREATE ON SCHEMA public TO geotabadapter_reader;

-- Ensure future tables/sequences are accessible
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO geotabadapter_client;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT USAGE, SELECT ON SEQUENCES TO geotabadapter_client;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT, INSERT, UPDATE, DELETE ON TABLES TO geotabadapter_reader;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT USAGE, SELECT ON SEQUENCES TO geotabadapter_reader;

-- Keep pgadmin as superuser for administration
GRANT ALL PRIVILEGES ON SCHEMA public TO pgadmin;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO pgadmin;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO pgadmin;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON TABLES TO pgadmin;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL PRIVILEGES ON SEQUENCES TO pgadmin;