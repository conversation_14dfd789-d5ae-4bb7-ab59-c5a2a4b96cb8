﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.32112.339
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MyGeotabAPIAdapter", "MyGeotabAPIAdapter\MyGeotabAPIAdapter.csproj", "{DC828638-F0A2-4D3C-8726-0D9F694EE699}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MyGeotabAPIAdapter.Tests", "MyGeotabAPIAdapter.Tests\MyGeotabAPIAdapter.Tests.csproj", "{389593BC-1F02-466D-AC8F-ED757C6D045E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MyGeotabAPIAdapter.Database", "MyGeotabAPIAdapter.Database\MyGeotabAPIAdapter.Database.csproj", "{3525DB4B-3603-4EF7-94CF-F1BAA1E8F9EC}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{DE34361C-1C64-4D86-9D77-ECF12B7EC1C0}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		README.md = README.md
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MyGeotabAPIAdapter.DataOptimizer", "MyGeotabAPIAdapter.DataOptimizer\MyGeotabAPIAdapter.DataOptimizer.csproj", "{B5BD8A37-D7B6-4C48-A015-F2D5FF62EC7C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MyGeotabAPIAdapter.Logging", "MyGeotabAPIAdapter.Logging\MyGeotabAPIAdapter.Logging.csproj", "{D81C30E1-44FE-4E1C-BCCE-89FBE370D08C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MyGeotabAPIAdapter.Exceptions", "MyGeotabAPIAdapter.Exceptions\MyGeotabAPIAdapter.Exceptions.csproj", "{ED993265-3C2B-4FFA-A81E-D5055814B113}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MyGeotabAPIAdapter.Helpers", "MyGeotabAPIAdapter.Helpers\MyGeotabAPIAdapter.Helpers.csproj", "{BCF76067-F0EA-4345-906C-162EA3AB6DAE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MyGeotabAPIAdapter.MyGeotabAPI", "MyGeotabAPIAdapter.MyGeotabAPI\MyGeotabAPIAdapter.MyGeotabAPI.csproj", "{5D190EFB-6575-4F61-A081-07CC7567D57E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MyGeotabAPIAdapter.Configuration", "MyGeotabAPIAdapter.Configuration\MyGeotabAPIAdapter.Configuration.csproj", "{1CA605AA-2A98-48A6-B8A5-7CFBA7DD54C0}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MyGeotabAPIAdapter.Database.EntityPersisters", "MyGeotabAPIAdapter.Database.EntityPersisters\MyGeotabAPIAdapter.Database.EntityPersisters.csproj", "{B4B64C84-96A9-43A7-B593-25C256382D5C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MyGeotabAPIAdapter.Geospatial", "MyGeotabAPIAdapter.Geospatial\MyGeotabAPIAdapter.Geospatial.csproj", "{6F7B27AC-E0E4-4393-9F41-2AA080A8F0C7}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "DeployToAzure", "DeployToAzure", "{A40F9DBE-A35C-455C-B574-EE41A7D2535A}"
	ProjectSection(SolutionItems) = preProject
		DeployToAzure\ARM_Template.json = DeployToAzure\ARM_Template.json
		DeployToAzure\ARM_TemplateParameterValues.json = DeployToAzure\ARM_TemplateParameterValues.json
		DeployToAzure\AzureCLI_ResourceCreationScript.ps1 = DeployToAzure\AzureCLI_ResourceCreationScript.ps1
		DeployToAzure\MyGeotabAPIAdapterDeploymentScript.ps1 = DeployToAzure\MyGeotabAPIAdapterDeploymentScript.ps1
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MyGeotabAPIAdapter.GeotabObjectMappers", "MyGeotabAPIAdapter.GeotabObjectMappers\MyGeotabAPIAdapter.GeotabObjectMappers.csproj", "{F0D05A23-BB61-4D70-9910-25CA44CC80FF}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DC828638-F0A2-4D3C-8726-0D9F694EE699}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DC828638-F0A2-4D3C-8726-0D9F694EE699}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DC828638-F0A2-4D3C-8726-0D9F694EE699}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DC828638-F0A2-4D3C-8726-0D9F694EE699}.Release|Any CPU.Build.0 = Release|Any CPU
		{389593BC-1F02-466D-AC8F-ED757C6D045E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{389593BC-1F02-466D-AC8F-ED757C6D045E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{389593BC-1F02-466D-AC8F-ED757C6D045E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{389593BC-1F02-466D-AC8F-ED757C6D045E}.Release|Any CPU.Build.0 = Release|Any CPU
		{3525DB4B-3603-4EF7-94CF-F1BAA1E8F9EC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3525DB4B-3603-4EF7-94CF-F1BAA1E8F9EC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3525DB4B-3603-4EF7-94CF-F1BAA1E8F9EC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3525DB4B-3603-4EF7-94CF-F1BAA1E8F9EC}.Release|Any CPU.Build.0 = Release|Any CPU
		{B5BD8A37-D7B6-4C48-A015-F2D5FF62EC7C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B5BD8A37-D7B6-4C48-A015-F2D5FF62EC7C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B5BD8A37-D7B6-4C48-A015-F2D5FF62EC7C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B5BD8A37-D7B6-4C48-A015-F2D5FF62EC7C}.Release|Any CPU.Build.0 = Release|Any CPU
		{D81C30E1-44FE-4E1C-BCCE-89FBE370D08C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D81C30E1-44FE-4E1C-BCCE-89FBE370D08C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D81C30E1-44FE-4E1C-BCCE-89FBE370D08C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D81C30E1-44FE-4E1C-BCCE-89FBE370D08C}.Release|Any CPU.Build.0 = Release|Any CPU
		{ED993265-3C2B-4FFA-A81E-D5055814B113}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ED993265-3C2B-4FFA-A81E-D5055814B113}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ED993265-3C2B-4FFA-A81E-D5055814B113}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ED993265-3C2B-4FFA-A81E-D5055814B113}.Release|Any CPU.Build.0 = Release|Any CPU
		{BCF76067-F0EA-4345-906C-162EA3AB6DAE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BCF76067-F0EA-4345-906C-162EA3AB6DAE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BCF76067-F0EA-4345-906C-162EA3AB6DAE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BCF76067-F0EA-4345-906C-162EA3AB6DAE}.Release|Any CPU.Build.0 = Release|Any CPU
		{5D190EFB-6575-4F61-A081-07CC7567D57E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5D190EFB-6575-4F61-A081-07CC7567D57E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5D190EFB-6575-4F61-A081-07CC7567D57E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5D190EFB-6575-4F61-A081-07CC7567D57E}.Release|Any CPU.Build.0 = Release|Any CPU
		{1CA605AA-2A98-48A6-B8A5-7CFBA7DD54C0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1CA605AA-2A98-48A6-B8A5-7CFBA7DD54C0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1CA605AA-2A98-48A6-B8A5-7CFBA7DD54C0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1CA605AA-2A98-48A6-B8A5-7CFBA7DD54C0}.Release|Any CPU.Build.0 = Release|Any CPU
		{B4B64C84-96A9-43A7-B593-25C256382D5C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B4B64C84-96A9-43A7-B593-25C256382D5C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B4B64C84-96A9-43A7-B593-25C256382D5C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B4B64C84-96A9-43A7-B593-25C256382D5C}.Release|Any CPU.Build.0 = Release|Any CPU
		{6F7B27AC-E0E4-4393-9F41-2AA080A8F0C7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6F7B27AC-E0E4-4393-9F41-2AA080A8F0C7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6F7B27AC-E0E4-4393-9F41-2AA080A8F0C7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6F7B27AC-E0E4-4393-9F41-2AA080A8F0C7}.Release|Any CPU.Build.0 = Release|Any CPU
		{F0D05A23-BB61-4D70-9910-25CA44CC80FF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F0D05A23-BB61-4D70-9910-25CA44CC80FF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F0D05A23-BB61-4D70-9910-25CA44CC80FF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F0D05A23-BB61-4D70-9910-25CA44CC80FF}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{A40F9DBE-A35C-455C-B574-EE41A7D2535A} = {DE34361C-1C64-4D86-9D77-ECF12B7EC1C0}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {28B1EC99-3BB0-4B69-8342-3DB08CE2B3F4}
	EndGlobalSection
EndGlobal
