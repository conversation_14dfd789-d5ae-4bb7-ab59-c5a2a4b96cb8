#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

function question(prompt) {
    return new Promise((resolve) => {
        rl.question(prompt, resolve);
    });
}

async function configureMyGeotab() {
    console.log('\n🚛 MyGeotab API Adapter Configuration Wizard');
    console.log('='.repeat(50));
    
    console.log('\nThis wizard will configure your MyGeotab credentials for the official adapter.');
    console.log('Your credentials will be saved to the adapter configuration file.\n');

    // Get MyGeotab credentials
    const server = await question('MyGeotab Server (default: my.geotab.com): ') || 'my.geotab.com';
    const database = await question('MyGeotab Database: ');
    const username = await question('MyGeotab Username: ');
    const password = await question('MyGeotab Password: ');

    if (!database || !username || !password) {
        console.log('\n❌ Error: All MyGeotab credentials are required.');
        process.exit(1);
    }

    // Update the configuration file
    const configPath = path.join(__dirname, 'official-adapter', 'MyGeotabAPIAdapter_SCD_win-x64', 'appsettings.json');
    
    try {
        const configContent = fs.readFileSync(configPath, 'utf8');
        const config = JSON.parse(configContent);

        // Update login settings
        config.LoginSettings.MyGeotabServer = server;
        config.LoginSettings.MyGeotabDatabase = database;
        config.LoginSettings.MyGeotabUser = username;
        config.LoginSettings.MyGeotabPassword = password;

        // Write back to file
        fs.writeFileSync(configPath, JSON.stringify(config, null, 2));

        console.log('\n✅ Configuration saved successfully!');
        console.log('\nNext steps:');
        console.log('1. Start the adapter: npm run start-adapter');
        console.log('2. Monitor logs: npm run logs-adapter');
        console.log('3. View data in Grafana: http://localhost:3000');
        
    } catch (error) {
        console.error('\n❌ Error updating configuration:', error.message);
        process.exit(1);
    }

    rl.close();
}

// Run the configuration
configureMyGeotab().catch(console.error);
