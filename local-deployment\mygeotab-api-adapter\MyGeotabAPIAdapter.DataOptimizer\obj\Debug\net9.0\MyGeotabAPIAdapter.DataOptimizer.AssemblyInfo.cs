//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Reflection;

[assembly: Microsoft.Extensions.Configuration.UserSecrets.UserSecretsIdAttribute("dotnet-MyGeotabAPIAdapter.DataOptimizer-869D4350-5E76-4BAE-AC1D-08B82AF48ECA")]
[assembly: System.Reflection.AssemblyCompanyAttribute("Geotab Inc.")]
[assembly: System.Reflection.AssemblyConfigurationAttribute("Debug")]
[assembly: System.Reflection.AssemblyDescriptionAttribute(@"A worker service designed to migrate data from the MyGeotab API Adapter database into another set of tables that are optimized for use by applications and data analysis tools. Additional columns are added to some of the tables and these are populated via interpolation or other query-based procedues.")]
[assembly: System.Reflection.AssemblyFileVersionAttribute("*******")]
[assembly: System.Reflection.AssemblyInformationalVersionAttribute("1.0.0+b0fba51ee465f39f66a3aaaa0c829cfa3a09785b")]
[assembly: System.Reflection.AssemblyProductAttribute("MyGeotab API Adapter - DataOptimizer")]
[assembly: System.Reflection.AssemblyTitleAttribute("MyGeotabAPIAdapter.DataOptimizer")]
[assembly: System.Reflection.AssemblyVersionAttribute("*******")]

// Generated by the MSBuild WriteCodeFragment class.

