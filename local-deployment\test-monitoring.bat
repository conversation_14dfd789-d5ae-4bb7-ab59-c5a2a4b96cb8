@echo off
echo ========================================
echo Testing Monitoring Stack
echo ========================================
echo.

echo [1/5] Testing Loki API...
curl -s "http://localhost:3100/ready" >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo ✅ Loki API is responding
) else (
    echo ❌ Loki API not responding
)

echo.
echo [2/5] Testing Loki labels (should show container names)...
curl -s "http://localhost:3100/loki/api/v1/label" 2>nul | findstr container_name >nul
if %ERRORLEVEL% equ 0 (
    echo ✅ Loki is receiving logs with labels
) else (
    echo ❌ Loki not receiving labeled logs
)

echo.
echo [3/5] Testing Netdata...
curl -s "http://localhost:19999/api/v1/info" >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo ✅ Netdata is responding
) else (
    echo ❌ Netdata not responding
)

echo.
echo [4/5] Testing Uptime Kuma...
curl -s "http://localhost:3001" >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo ✅ Uptime Kuma is responding
) else (
    echo ❌ Uptime Kuma not responding
)

echo.
echo [5/5] Testing pgAdmin...
curl -s "http://localhost:5050/misc/ping" >nul 2>&1
if %ERRORLEVEL% equ 0 (
    echo ✅ pgAdmin is responding
) else (
    echo ❌ pgAdmin not responding
)

echo.
echo ========================================
echo NEXT STEPS:
echo ========================================
echo 1. Access Grafana: http://localhost:3000
echo    - Go to Explore → Select Loki
echo    - Query: {container_name!=""}
echo.
echo 2. Access pgAdmin: http://localhost:5050
echo    - Login: <EMAIL> / monitoring123
echo    - Add server connection (see manual setup guide)
echo.
echo 3. Access Netdata: http://localhost:19999
echo    - Check Applications → PostgreSQL
echo    - Check Containers section
echo.
echo 4. Access Uptime Kuma: http://localhost:3001
echo    - Create your account on first visit
echo    - Set up monitors (see manual setup guide)
echo.
pause
