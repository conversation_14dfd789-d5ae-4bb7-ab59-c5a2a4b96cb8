﻿using Geotab.Checkmate.ObjectModel;
using MyGeotabAPIAdapter.Database.Models;

namespace MyGeotabAPIAdapter.GeotabObjectMappers
{
    /// <summary>
    /// Interface for a class with methods involving mapping between <see cref="DutyStatusAvailability"/> and <see cref="DbDutyStatusAvailability"/> entities.
    /// </summary>
    public interface IGeotabDutyStatusAvailabilityDbDutyStatusAvailabilityObjectMapper : IGeotabObjectMapper<DutyStatusAvailability, DbDutyStatusAvailability>
    {
    }
}
