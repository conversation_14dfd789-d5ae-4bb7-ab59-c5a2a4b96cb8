@echo off
REM Comprehensive System Status Check Script

echo ========================================
echo MyGeotab Adapter System Status
echo ========================================
echo.

echo [DOCKER] Docker Services Status:
echo ----------------------------------------
docker-compose ps
echo.

echo [ADAPTER] MyGeotab Adapter Process:
echo ----------------------------------------
tasklist /FI "IMAGENAME eq MyGeotabAPIAdapter.exe" 2>NUL | find /I /N "MyGeotabAPIAdapter.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [OK] MyGeotab Adapter is RUNNING

    REM Get detailed process information
    for /f "tokens=2,5 delims=," %%i in ('tasklist /FI "IMAGENAME eq MyGeotabAPIAdapter.exe" /FO CSV ^| find "MyGeotabAPIAdapter.exe"') do (
        echo    Process ID: %%~i
        echo    Memory Usage: %%~j
    )

    REM Check how long it's been running
    powershell -Command "$process = Get-Process -Name MyGeotabAPIAdapter -ErrorAction SilentlyContinue; if ($process) { $uptime = (Get-Date) - $process.StartTime; Write-Host '    Uptime:' $uptime.Days 'days' $uptime.Hours 'hours' $uptime.Minutes 'minutes' }"

) else (
    echo [ERROR] MyGeotab Adapter is NOT RUNNING
    echo    Use: npm run start-adapter-service
)
echo.

echo [NETWORK] Service Connectivity:
echo ----------------------------------------
echo Testing Grafana...
powershell -Command "$ProgressPreference = 'SilentlyContinue'; try { Invoke-WebRequest -Uri http://localhost:3000 -UseBasicParsing -TimeoutSec 5 | Out-Null; Write-Host '[OK] Grafana: http://localhost:3000' } catch { Write-Host '[ERROR] Grafana: Not accessible' }"

echo Testing PgHero...
powershell -Command "$ProgressPreference = 'SilentlyContinue'; try { Invoke-WebRequest -Uri http://localhost:8081 -UseBasicParsing -TimeoutSec 5 | Out-Null; Write-Host '[OK] PgHero: http://localhost:8081 (admin/pghero123)' } catch { Write-Host '[ERROR] PgHero: Not accessible' }"

echo Testing Dozzle...
powershell -Command "$ProgressPreference = 'SilentlyContinue'; try { Invoke-WebRequest -Uri http://localhost:8080 -UseBasicParsing -TimeoutSec 5 | Out-Null; Write-Host '[OK] Dozzle: http://localhost:8080' } catch { Write-Host '[ERROR] Dozzle: Not accessible' }"
echo.

echo [DATABASE] Database Status:
echo ----------------------------------------
node test-adapter.js
echo.

echo [ACTIONS] Quick Actions:
echo ----------------------------------------
echo   Start Adapter:    start-adapter-service.bat
echo   Stop Adapter:     stop-adapter-service.bat
echo   View Logs:        npm run local-logs
echo   Start Services:   npm run local-deploy
echo   Stop Services:    npm run local-stop
echo   Test Database:    npm run test-adapter
echo   Configure:        npm run configure-mygeotab
echo.
pause
