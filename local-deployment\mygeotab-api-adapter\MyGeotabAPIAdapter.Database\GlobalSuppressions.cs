﻿// This file is used by Code Analysis to maintain SuppressMessage
// attributes that are applied to this project.
// Project-level suppressions either have no target or are given
// a specific target and scoped to a namespace, type, member, etc.

using System.Diagnostics.CodeAnalysis;

[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbCondition.id")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbConfigFeedVersion.id")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbDevice.id")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbDeviceStatusInfo.id")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbDiagnostic.id")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbDVIRDefect.id")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbDVIRDefectRemark.id")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbDVIRLog.id")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbExceptionEvent.id")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbFaultData.id")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbLogRecord.id")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbRule.id")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbStatusData.id")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbTrip.id")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbUser.id")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbZone.id")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbZoneType.id")]
[assembly: SuppressMessage("Style", "IDE0057:Use range operator", Justification = "<Pending>", Scope = "member", Target = "~M:Dapper.Contrib.Extensions.SqlMapperExtensions.GetTableName(System.Type)~System.String")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbDutyStatusAvailability.id")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbDVIRDefectUpdate.id")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbFailedDVIRDefectUpdate.id")]
[assembly: SuppressMessage("Style", "IDE0090:Use 'new(...)'", Justification = "<Pending>", Scope = "member", Target = "~F:Dapper.Contrib.Extensions.SqlMapperExtensions.AdapterDictionary")]
[assembly: SuppressMessage("Style", "IDE0090:Use 'new(...)'", Justification = "<Pending>", Scope = "member", Target = "~F:Dapper.Contrib.Extensions.SqlMapperExtensions.ComputedProperties")]
[assembly: SuppressMessage("Style", "IDE0090:Use 'new(...)'", Justification = "<Pending>", Scope = "member", Target = "~F:Dapper.Contrib.Extensions.SqlMapperExtensions.ExplicitKeyProperties")]
[assembly: SuppressMessage("Style", "IDE0090:Use 'new(...)'", Justification = "<Pending>", Scope = "member", Target = "~F:Dapper.Contrib.Extensions.SqlMapperExtensions.GetQueries")]
[assembly: SuppressMessage("Style", "IDE0090:Use 'new(...)'", Justification = "<Pending>", Scope = "member", Target = "~F:Dapper.Contrib.Extensions.SqlMapperExtensions.KeyProperties")]
[assembly: SuppressMessage("Style", "IDE0090:Use 'new(...)'", Justification = "<Pending>", Scope = "member", Target = "~F:Dapper.Contrib.Extensions.SqlMapperExtensions.ProxyGenerator.TypeCache")]
[assembly: SuppressMessage("Style", "IDE0090:Use 'new(...)'", Justification = "<Pending>", Scope = "member", Target = "~F:Dapper.Contrib.Extensions.SqlMapperExtensions.TypeProperties")]
[assembly: SuppressMessage("Style", "IDE0090:Use 'new(...)'", Justification = "<Pending>", Scope = "member", Target = "~F:Dapper.Contrib.Extensions.SqlMapperExtensions.TypeTableName")]
[assembly: SuppressMessage("Usage", "CA2211:Non-constant fields should not be visible", Justification = "<Pending>", Scope = "member", Target = "~F:Dapper.Contrib.Extensions.SqlMapperExtensions.GetDatabaseType")]
[assembly: SuppressMessage("Usage", "CA2211:Non-constant fields should not be visible", Justification = "<Pending>", Scope = "member", Target = "~F:Dapper.Contrib.Extensions.SqlMapperExtensions.TableNameMapper")]
[assembly: SuppressMessage("Performance", "CA1825:Avoid zero-length array allocations", Justification = "<Pending>", Scope = "member", Target = "~M:Dapper.Contrib.Extensions.SqlMapperExtensions.ProxyGenerator.CreateProperty``1(System.Reflection.Emit.TypeBuilder,System.String,System.Type,System.Reflection.MethodInfo,System.Boolean)")]
[assembly: SuppressMessage("Design", "CA1050:Declare types in namespaces", Justification = "<Pending>", Scope = "type", Target = "~T:FbAdapter")]
[assembly: SuppressMessage("Design", "CA1050:Declare types in namespaces", Justification = "<Pending>", Scope = "type", Target = "~T:ISqlAdapter")]
[assembly: SuppressMessage("Design", "CA1050:Declare types in namespaces", Justification = "<Pending>", Scope = "type", Target = "~T:MySqlAdapter")]
[assembly: SuppressMessage("Design", "CA1050:Declare types in namespaces", Justification = "<Pending>", Scope = "type", Target = "~T:PostgresAdapter")]
[assembly: SuppressMessage("Design", "CA1050:Declare types in namespaces", Justification = "<Pending>", Scope = "type", Target = "~T:SqlCeServerAdapter")]
[assembly: SuppressMessage("Design", "CA1050:Declare types in namespaces", Justification = "<Pending>", Scope = "type", Target = "~T:SqlServerAdapter")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Database.Caches.GenericIdCache`1.RefreshAsync~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbDriverChange.id")]
[assembly: SuppressMessage("Design", "CA1050:Declare types in namespaces", Justification = "<Pending>", Scope = "type", Target = "~T:OracleAdapter")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.DbOProcessorTracking.id")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.Add_Ons.VSS.DbFailedOVDSServerCommand.id")]
[assembly: SuppressMessage("Style", "IDE1006:Naming Styles", Justification = "<Pending>", Scope = "member", Target = "~P:MyGeotabAPIAdapter.Database.Models.Add_Ons.VSS.DbOVDSServerCommand.id")]
[assembly: SuppressMessage("Style", "IDE0074:Use compound assignment", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Database.Caches.AdapterGenericDbObjectCache`1.UpdateAsync(System.Boolean)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0074:Use compound assignment", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Database.Caches.GenericGeotabGUIDCacheableDbObjectCache2`1.UpdateAsync(System.Boolean)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0074:Use compound assignment", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Database.Caches.OptimizerGenericDbObjectCache`1.UpdateAsync(System.Boolean)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0060:Remove unused parameter", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Database.Caches.GenericGenericDbObjectCache`2.#ctor(`0,`1)")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Database.Caches.AdapterGenericDbObjectCache`1.UpdateAsync(System.Boolean)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Database.Caches.GenericGeotabGUIDCacheableDbObjectCache2`1.UpdateAsync(System.Boolean)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Database.Caches.OptimizerGenericDbObjectCache`1.UpdateAsync(System.Boolean)~System.Threading.Tasks.Task")]
