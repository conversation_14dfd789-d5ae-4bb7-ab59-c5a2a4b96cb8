# Enterprise Monitoring Stack - Manual Setup Guide

This guide covers manual configuration steps that cannot be automated after running `setup-monitoring.bat`.

## 🚀 Quick Start

After running the automated setup, you'll need to manually configure:
1. **Uptime <PERSON><PERSON> monitors** - Set up service monitoring
2. **pgAdmin database connections** - Connect to PostgreSQL
3. **Notification channels** (optional) - Email/Slack alerts
4. **MyGeotab Adapter monitoring** (optional) - Database activity monitoring

---

## 1. Uptime Kuma Configuration

### Initial Setup
1. **Access Uptime Kuma:** http://localhost:3001
2. **First-time setup:** Create admin account
   - Username: `admin`
   - Password: `monitoring123`
   - Email: `<EMAIL>`

### Essential Monitors to Create

#### A. Web Service Monitors
Create these HTTP monitors:

**Grafana Monitor:**
- Monitor Type: `HTTP(s)`
- Friendly Name: `Grafana Dashboard`
- URL: `http://grafana:3000`
- Heartbeat Interval: `60 seconds`
- Retries: `3`
- Expected Status Code: `200`

**PgHero Monitor:**
- Monitor Type: `HTTP(s)`
- Friendly Name: `PgHero Database Monitor`
- URL: `http://pghero:8080`
- Heartbeat Interval: `60 seconds`
- Retries: `3`

**PostgreSQL Monitor:**
- Monitor Type: `Port`
- Friendly Name: `PostgreSQL Database`
- Hostname: `postgres`
- Port: `5432`
- Heartbeat Interval: `60 seconds`

#### B. MyGeotab Adapter Activity Monitor (Recommended)

**Database Activity Monitor:**
- Monitor Type: `PostgreSQL`
- Friendly Name: `MyGeotab Adapter Activity`
- Connection String: `**********************************************/geotabadapterdb`
- SQL Query:
  ```sql
  SELECT COUNT(*) as recent_records 
  FROM "LogRecords2" 
  WHERE "DateTime" > NOW() - INTERVAL '5 minutes'
  ```
- Expected Result: `> 0` (adapter is active if GPS data is recent)
- Heartbeat Interval: `300 seconds` (5 minutes)
- Retries: `2`

### Notification Setup (Optional)

#### Email Notifications:
1. Go to **Settings** → **Notifications**
2. Click **Setup Notification**
3. Choose **Email (SMTP)**
4. Configure your SMTP settings:
   ```
   SMTP Host: your-smtp-server.com
   Port: 587 (or 465 for SSL)
   Security: STARTTLS
   Username: <EMAIL>
   Password: your-app-password
   From Email: <EMAIL>
   To Email: <EMAIL>
   ```

#### Slack Notifications:
1. Create a Slack webhook URL in your Slack workspace
2. In Uptime Kuma: **Settings** → **Notifications** → **Slack**
3. Enter your webhook URL
4. Test the notification

---

## 2. pgAdmin Configuration

### Initial Setup
1. **Access pgAdmin:** http://localhost:5050
2. **Login:**
   - Email: `<EMAIL>`
   - Password: `monitoring123`

### Add PostgreSQL Server Connection

1. **Right-click "Servers"** → **Create** → **Server**
2. **General Tab:**
   - Name: `MyGeotab Fleet Database`
3. **Connection Tab:**
   - Host: `postgres`
   - Port: `5432`
   - Maintenance database: `geotabadapterdb`
   - Username: `pgadmin`
   - Password: `localdev123`
4. **Advanced Tab:**
   - DB restriction: `geotabadapterdb`
5. **Save**

### Useful pgAdmin Features

**Query Tool Shortcuts:**
- View recent GPS data: `SELECT * FROM "LogRecords2" ORDER BY "DateTime" DESC LIMIT 100`
- Check adapter activity: `SELECT COUNT(*) FROM "LogRecords2" WHERE "DateTime" > NOW() - INTERVAL '1 hour'`
- Database size: `SELECT pg_size_pretty(pg_database_size('geotabadapterdb'))`

---

## 3. Grafana Loki Integration

### Verify Loki Datasource
1. **Access Grafana:** http://localhost:3000 (admin/admin123)
2. **Go to:** Configuration → Data Sources
3. **Verify "Loki"** datasource exists and is working
4. **Test connection** - should show green checkmark

### Create Log Dashboard (Optional)
1. **Create new dashboard**
2. **Add panel** with Loki datasource
3. **Example queries:**
   ```
   {container_name="postgres"}
   {container_name="grafana"} |= "error"
   {service_name="mygeotab-adapter"}
   ```

---

## 4. Watchtower Notifications (Optional)

### Email Notifications
Edit `docker-compose.monitoring.yml` and update Watchtower environment:

```yaml
watchtower:
  environment:
    WATCHTOWER_NOTIFICATIONS: "email"
    WATCHTOWER_NOTIFICATION_EMAIL_FROM: "<EMAIL>"
    WATCHTOWER_NOTIFICATION_EMAIL_TO: "<EMAIL>"
    WATCHTOWER_NOTIFICATION_EMAIL_SERVER: "smtp.gmail.com"
    WATCHTOWER_NOTIFICATION_EMAIL_SERVER_PORT: "587"
    WATCHTOWER_NOTIFICATION_EMAIL_SERVER_USER: "<EMAIL>"
    WATCHTOWER_NOTIFICATION_EMAIL_SERVER_PASSWORD: "your-app-password"
```

Then restart: `npm run monitoring-restart`

### Slack Notifications
```yaml
watchtower:
  environment:
    WATCHTOWER_NOTIFICATIONS: "slack"
    WATCHTOWER_NOTIFICATION_SLACK_HOOK_URL: "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
    WATCHTOWER_NOTIFICATION_SLACK_IDENTIFIER: "Fleet Watchtower"
```

---

## 5. Advanced Configuration

### Netdata Cloud (Optional)
1. **Access Netdata:** http://localhost:19999
2. **Sign up** for Netdata Cloud (free)
3. **Claim node** using the provided command
4. **Benefits:** Remote monitoring, mobile alerts, historical data

### Loki Log Retention
To change from 7-day default, edit `monitoring/loki/loki-config.yml`:
```yaml
limits_config:
  retention_period: 336h  # 14 days
table_manager:
  retention_period: 336h  # 14 days
```

---

## 6. Troubleshooting

### Common Issues

**Uptime Kuma can't reach services:**
- Ensure all services are on `monitoring-network`
- Use container names (e.g., `grafana`, not `localhost`)

**pgAdmin connection fails:**
- Verify PostgreSQL is running: `docker ps | findstr postgres`
- Check network connectivity: `docker exec pgadmin ping postgres`

**Loki not receiving logs:**
- Check Promtail status: `docker logs promtail`
- Verify Docker socket access: `docker exec promtail ls -la /var/run/docker.sock`

**Grafana can't connect to Loki:**
- Restart Grafana: `docker restart grafana`
- Check Loki health: `curl http://localhost:3100/ready`

### Health Check Commands
```bash
# Check all monitoring services
npm run monitoring-status

# View specific service logs
docker logs netdata
docker logs uptime-kuma
docker logs loki
docker logs promtail
docker logs pgadmin

# Test service connectivity
curl http://localhost:19999/api/v1/info    # Netdata
curl http://localhost:3001                 # Uptime Kuma
curl http://localhost:3100/ready           # Loki
curl http://localhost:5050/misc/ping       # pgAdmin
```

---

## 📋 Summary Checklist

After completing this manual setup:

- [ ] Uptime Kuma monitors created for all services
- [ ] MyGeotab Adapter activity monitor configured
- [ ] pgAdmin connected to PostgreSQL database
- [ ] Notification channels configured (email/Slack)
- [ ] Grafana Loki datasource verified
- [ ] All services accessible and healthy

**Next Steps:**
- Monitor your fleet data in Grafana dashboards
- Check Uptime Kuma for service health
- Use pgAdmin for database administration
- Review logs in Grafana using Loki datasource
- Set up custom alerts based on your requirements
