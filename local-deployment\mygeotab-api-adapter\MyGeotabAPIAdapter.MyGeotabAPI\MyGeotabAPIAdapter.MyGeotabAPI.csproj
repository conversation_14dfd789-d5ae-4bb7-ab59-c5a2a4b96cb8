﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Geotab.Checkmate.ObjectModel" Version="11.68.266" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NLog.Extensions.Logging" Version="5.5.0" />
    <PackageReference Include="Polly" Version="8.5.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\MyGeotabAPIAdapter.Exceptions\MyGeotabAPIAdapter.Exceptions.csproj" />
    <ProjectReference Include="..\MyGeotabAPIAdapter.Helpers\MyGeotabAPIAdapter.Helpers.csproj" />
    <ProjectReference Include="..\MyGeotabAPIAdapter.Logging\MyGeotabAPIAdapter.Logging.csproj" />
  </ItemGroup>

</Project>
