# 🚗 MyGeotab Fleet Monitoring - Setup Guide

Complete setup guide for the MyGeotab Fleet Monitoring System using the Official Geotab MyGeotab API Adapter.

## 📋 Prerequisites

### Required Software
- **Docker Desktop** - Download from [docker.com](https://www.docker.com/products/docker-desktop/)
- **Windows Environment** - Required for the official MyGeotab API Adapter
- **Git** (optional) - For cloning the repository

### MyGeotab Account Requirements
- Active MyGeotab account with API access
- Database name and server URL
- Username and password with appropriate permissions

### System Requirements
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 10GB free space
- **Network**: Internet connection for MyGeotab API access

## 🚀 Installation Steps

### Step 1: Get the Code

```bash
# Clone the repository (if using Git)
git clone <repository-url>
cd mygeotab-fleet-monitoring

# Or download and extract the ZIP file
```

### Step 2: Start Docker Services

```bash
# Navigate to local deployment directory
cd local-deployment

# Start all infrastructure services
docker-compose up -d

# Wait for services to start (about 2 minutes)
```

### Step 3: Configure MyGeotab Connection

```bash
# Run the configuration wizard
npm run configure-mygeotab
```

The wizard will prompt you for:
- **MyGeotab Server**: Usually `my.geotab.com` or your custom server
- **Database Name**: Your MyGeotab database identifier
- **Username**: Your MyGeotab username
- **Password**: Your MyGeotab password

### Step 4: Start the Official Adapter

```bash
# Start the official MyGeotab API Adapter
npm run start-adapter
```

This will:
- Launch the official Geotab adapter executable
- Connect to your MyGeotab database
- Begin syncing data to PostgreSQL
- Create the complete official database schema

### Step 5: Verify Everything Works

```bash
# Test the adapter connection
npm run test-adapter
```

## 🌐 Access Your System

Once setup is complete, access these services:

| Service | URL | Credentials | Purpose |
|---------|-----|-------------|---------|
| **Configuration** | http://localhost:4000 | None | MyGeotab setup wizard |
| **Grafana** | http://localhost:3000 | admin/admin | Fleet dashboards |
| **PgHero** | http://localhost:8081 | None | Database monitoring |
| **Dozzle** | http://localhost:8080 | None | Container logs |

## 🔧 Configuration Details

### MyGeotab Adapter Configuration

The official adapter is configured with:
- **Data Feeds**: LogRecords, StatusData, FaultData, Trips
- **Sync Interval**: 30 seconds for real-time data
- **Database**: PostgreSQL with official Geotab schema
- **Caching**: Redis for improved performance

### Database Schema

The adapter creates these key tables:
- **Devices2** - Vehicle information (make, model, VIN)
- **LogRecords2** - GPS tracking data
- **StatusData2** - Engine diagnostics
- **FaultData2** - Fault codes and diagnostics
- **Trips2** - Trip data and analytics
- **Users2** - Driver information

## 🔍 Troubleshooting

### Common Issues

**Docker services won't start:**
- Ensure Docker Desktop is running
- Check that required ports are available
- Try `docker-compose down` then `docker-compose up -d`

**MyGeotab connection fails:**
- Verify credentials are correct
- Check MyGeotab server URL
- Ensure your account has API access
- Check Windows firewall settings

**No data appearing:**
- Wait 2-3 minutes for initial sync
- Check adapter is running and connected
- Verify database connection in PgHero

**Grafana can't connect to database:**
- Use hostname `postgres` not `localhost`
- Check credentials: pgadmin/localdev123
- Verify PostgreSQL container is healthy

### Reset Everything

If you need to start over:

```bash
# Stop all services and delete data
docker-compose down -v

# Remove any cached configurations
rm -f .env.mygeotab

# Start fresh
docker-compose up -d
```

## 📊 Monitoring Your Fleet

### Grafana Dashboards

Create dashboards for:
- **Real-time vehicle locations** on maps
- **Trip analytics** and reporting
- **Driver performance** metrics
- **Vehicle health** and diagnostics
- **Fuel consumption** tracking
- **Maintenance alerts** and scheduling

### Database Queries

Connect to PostgreSQL to run custom queries:

```bash
# Connect to database
docker exec -it postgres psql -U pgadmin -d monitoring_db

# Example queries
SELECT COUNT(*) FROM "Devices2";  -- Vehicle count
SELECT * FROM "LogRecords2" ORDER BY "DateTime" DESC LIMIT 10;  -- Latest GPS data
```

## 🔐 Security Considerations

### Local Development
- All services run on localhost only
- Default credentials are for development use
- No external network exposure

### Production Deployment
- Change default passwords
- Use environment variables for credentials
- Enable SSL/TLS encryption
- Configure firewall rules
- Regular security updates

## 🆘 Getting Help

### Documentation
- **Official Adapter**: [MyGeotab API Adapter GitHub](https://github.com/Geotab/mygeotab-api-adapter)
- **MyGeotab API**: [Geotab Developer Portal](https://developers.geotab.com/)
- **Grafana**: [Grafana Documentation](https://grafana.com/docs/)

### Support
- Check troubleshooting section above
- Review adapter logs for error messages
- Create issues in the repository for bugs
- Contact Geotab support for adapter-specific issues

## 🎯 Next Steps

1. **Explore Grafana** - Create custom dashboards for your fleet
2. **Set up alerts** - Configure notifications for critical events
3. **Customize queries** - Build reports specific to your needs
4. **Scale up** - Deploy to production environment when ready

---

**🚛 Your MyGeotab fleet monitoring system is now ready for use!**

The official adapter provides enterprise-grade reliability with complete Geotab support.
