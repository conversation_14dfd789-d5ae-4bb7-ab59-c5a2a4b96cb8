# PostgreSQL User Architecture - MyGeotab Adapter System

## Executive Summary

**STATUS: ✅ FULLY IMPLEMENTED AND VERIFIED**

The MyGeotab adapter system implements a secure three-user PostgreSQL architecture that provides proper separation of concerns for data input, data output, and administrative access.

## User Architecture Overview

### Three-User Security Model

| User | Purpose | Permissions | Used By |
|------|---------|-------------|---------|
| **pgadmin** | Administrative access | Full privileges (CREATE, DROP, ALTER, etc.) | System administration, testing |
| **geotabadapter_client** | Data input (write operations) | SELECT, INSERT, UPDATE, DELETE, CREATE | MyGeotab Adapter |
| **geotabadapter_reader** | Data output (read operations) | SELECT only | Grafana, PgHero |

### Data Flow Architecture

```text
MyGeotab API → MyGeotab Adapter → PostgreSQL → Grafana/PgHero
                     ↓                ↓              ↓
              geotabadapter_client  Database    geotabadapter_reader
                  (WRITE)          Storage         (READ)
                                      ↓
                                   pgadmin
                                  (ADMIN)
```

## Detailed User Specifications

### 1. pgadmin (Administrative User)

**Purpose:** Database administration and system management

**Permissions:**
- Full database privileges
- CREATE/DROP tables and schemas
- ALTER table structures
- GRANT/REVOKE permissions
- Database maintenance operations

**Used By:**
- Database administrators
- System setup scripts
- Test scripts (test-adapter.js)
- Manual database operations

**Connection Examples:**
```bash
# Administrative access
docker exec postgres psql -U pgadmin -d geotabadapterdb

# System testing
node test-adapter.js  # Uses pgadmin for comprehensive testing
```

### 2. geotabadapter_client (Data Input User)

**Purpose:** MyGeotab Adapter data synchronization (write operations)

**Permissions:**
- SELECT, INSERT, UPDATE, DELETE on all tables
- USAGE, SELECT on all sequences
- CREATE on schema public (for new tables)
- Future table privileges automatically granted

**Used By:**
- MyGeotab API Adapter (official .NET application)
- Data synchronization processes

**Configuration:**
```json
// appsettings.json
"DatabaseConnectionString": "Server=localhost;Port=5432;Database=geotabadapterdb;User Id=geotabadapter_client;Password=localdev123"
```

**Security Benefits:**
- Cannot drop or alter table structures
- Cannot grant permissions to other users
- Limited to data manipulation only
- Isolated from administrative functions

### 3. geotabadapter_reader (Data Output User)

**Purpose:** Dashboard and monitoring access (read-only operations)

**Permissions:**
- SELECT only on all tables
- No write, update, or delete permissions
- No schema modification capabilities
- Future table read access automatically granted

**Used By:**
- Grafana (dashboards and visualization)
- PgHero (database performance monitoring)
- Reporting systems

**Configuration:**
```yaml
# docker-compose.yml - Grafana
GF_DATABASE_USER: geotabadapter_reader
GF_DATABASE_PASSWORD: localdev123

# docker-compose.yml - PgHero  
DATABASE_URL: *********************************************************/geotabadapterdb
```

**Security Benefits:**
- Cannot modify any data
- Cannot affect system operations
- Safe for external reporting tools
- Prevents accidental data corruption

## Implementation Details

### Database Setup Script

**File:** `setup-postgres-users.sql`

**Key Operations:**
1. Creates users if they don't exist
2. Grants appropriate database connection privileges
3. Sets schema usage permissions
4. Configures table and sequence permissions
5. Establishes default privileges for future objects

**Execution:**
```bash
npm run setup-database
```

### Verification Commands

**Test User Connections:**
```bash
# Test reader user
docker exec postgres psql -U geotabadapter_reader -d geotabadapterdb -c "SELECT current_user;"

# Test client user  
docker exec postgres psql -U geotabadapter_client -d geotabadapterdb -c "SELECT current_user;"

# Test admin user
docker exec postgres psql -U pgadmin -d geotabadapterdb -c "SELECT current_user;"
```

**Verify Table Ownership:**
```bash
# List tables and their owners
docker exec postgres psql -U geotabadapter_reader -d geotabadapterdb -c "\dt"
```

## Security Benefits

### 1. Principle of Least Privilege
- Each user has only the minimum permissions required for their function
- Reduces attack surface and potential for accidental damage
- Limits scope of potential security breaches

### 2. Data Flow Isolation
- Write operations isolated to dedicated user
- Read operations cannot affect data integrity
- Administrative functions separated from operational access

### 3. Audit Trail
- Clear separation of who can perform what operations
- Database logs show which user performed each action
- Easier to track and investigate issues

### 4. Operational Safety
- Dashboards and monitoring cannot accidentally modify data
- Adapter failures cannot affect administrative functions
- Clear boundaries between system components

## Troubleshooting

### Common Issues

**1. Permission Denied Errors**
```bash
# Check user permissions
docker exec postgres psql -U pgadmin -d geotabadapterdb -c "\du"

# Re-run setup if needed
npm run setup-database
```

**2. Connection Failures**
```bash
# Verify user exists
docker exec postgres psql -U pgadmin -d geotabadapterdb -c "SELECT rolname FROM pg_roles WHERE rolname LIKE 'geotabadapter%';"

# Check service configuration
docker-compose config | grep -A5 -B5 DATABASE
```

**3. Table Access Issues**
```bash
# Check table permissions for specific user
docker exec postgres psql -U pgadmin -d geotabadapterdb -c "SELECT grantee, privilege_type FROM information_schema.role_table_grants WHERE table_name='Devices2';"
```

### Recovery Procedures

**Reset User Permissions:**
```bash
# Stop services
npm run down

# Reset database users
npm run setup-database

# Restart services
npm run up
```

## Best Practices

### 1. Regular Permission Audits
- Periodically review user permissions
- Ensure no privilege escalation has occurred
- Verify principle of least privilege is maintained

### 2. Password Management
- Use strong passwords in production
- Consider using environment variables for credentials
- Implement password rotation policies

### 3. Connection Monitoring
- Monitor database connections by user
- Set up alerts for unusual access patterns
- Log all administrative operations

### 4. Backup Considerations
- Ensure backup procedures account for all users
- Test restore procedures with user permissions
- Document user recreation procedures

## Production Deployment Notes

### Environment Variables
Consider using environment variables for production:
```bash
POSTGRES_ADMIN_PASSWORD=<strong-password>
GEOTAB_CLIENT_PASSWORD=<strong-password>  
GEOTAB_READER_PASSWORD=<strong-password>
```

### Network Security
- Restrict database access to necessary networks only
- Use SSL/TLS for database connections
- Implement firewall rules for database ports

### Monitoring
- Set up connection monitoring for each user
- Alert on failed authentication attempts
- Monitor for privilege escalation attempts

## Conclusion

The three-user PostgreSQL architecture provides enterprise-grade security and operational safety for the MyGeotab adapter system. This implementation follows database security best practices while maintaining operational simplicity and clear separation of concerns.

**Status: ✅ Production Ready**
- All users properly configured
- Permissions correctly assigned
- Security boundaries established
- Documentation complete
