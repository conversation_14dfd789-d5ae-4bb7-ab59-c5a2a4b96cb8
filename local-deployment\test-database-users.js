const { Client } = require('pg');

async function testDatabaseUsers() {
    console.log('[TEST] Testing database user setup...');
    
    // Test geotabadapter_client user (used by MyGeotab Adapter)
    try {
        const clientUser = new Client({
            host: 'localhost',
            port: 5432,
            database: 'geotabadapterdb',
            user: 'geotabadapter_client',
            password: 'localdev123'
        });
        
        await clientUser.connect();
        await clientUser.query('SELECT 1');
        await clientUser.end();
        console.log('[OK] geotabadapter_client user working');
    } catch (error) {
        console.log('[ERROR] geotabadapter_client user failed:', error.message);
        process.exit(1);
    }
    
    // Test geotabadapter_reader user (used by Grafana/PgHero)
    try {
        const readerUser = new Client({
            host: 'localhost',
            port: 5432,
            database: 'geotabadapterdb',
            user: 'geotabadapter_reader',
            password: 'localdev123'
        });
        
        await readerUser.connect();
        await readerUser.query('SELECT 1');
        await readerUser.end();
        console.log('[OK] geotabadapter_reader user working');
    } catch (error) {
        console.log('[ERROR] geotabadapter_reader user failed:', error.message);
        process.exit(1);
    }
    
    console.log('[OK] All database users configured correctly');
    process.exit(0);
}

testDatabaseUsers().catch(error => {
    console.log('[ERROR] Database user test failed:', error.message);
    process.exit(1);
});
