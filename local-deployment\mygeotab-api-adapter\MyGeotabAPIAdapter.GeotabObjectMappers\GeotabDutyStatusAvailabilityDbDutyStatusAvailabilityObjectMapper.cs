﻿using Geotab.Checkmate.ObjectModel;
using MyGeotabAPIAdapter.Database;
using MyGeotabAPIAdapter.Database.Models;
using Newtonsoft.Json;

namespace MyGeotabAPIAdapter.GeotabObjectMappers
{
    /// <summary>
    /// A class with methods involving mapping between <see cref="DutyStatusAvailability"/> and <see cref="DbDutyStatusAvailability"/> entities.
    /// </summary>
    public class GeotabDutyStatusAvailabilityDbDutyStatusAvailabilityObjectMapper : IGeotabDutyStatusAvailabilityDbDutyStatusAvailabilityObjectMapper
    {
        /// <inheritdoc/>
        public DbDutyStatusAvailability CreateEntity(DutyStatusAvailability entityToMapTo)
        {
            DbDutyStatusAvailability dbDutyStatusAvailability = new()
            {
                Cycle = entityToMapTo.Cycle,
                CycleRest = entityToMapTo.CycleRest,
                DatabaseWriteOperationType = Common.DatabaseWriteOperationType.Insert,
                DriverId = entityToMapTo.Driver.Id.ToString(),
                Driving = entityToMapTo.Driving,
                Duty = entityToMapTo.Duty,
                DutySinceCycleRest = entityToMapTo.DutySinceCycleRest,
                Is16HourExemptionAvailable = entityToMapTo.Is16HourExemptionAvailable,
                IsAdverseDrivingExemptionAvailable = entityToMapTo.IsAdverseDrivingExemptionAvailable,
                IsOffDutyDeferralExemptionAvailable = entityToMapTo.IsOffDutyDeferralExemptionAvailable,
                RecordLastChangedUtc = DateTime.UtcNow,
                Rest = entityToMapTo.Rest,
                Workday = entityToMapTo.Workday
            };

            string cycleAvailabilities = JsonConvert.SerializeObject(entityToMapTo.CycleAvailabilities);
            dbDutyStatusAvailability.CycleAvailabilities = cycleAvailabilities;

            string recap = JsonConvert.SerializeObject(entityToMapTo.Recap);
            dbDutyStatusAvailability.Recap = recap;

            return dbDutyStatusAvailability;
        }

        /// <inheritdoc/>
        public bool EntityRequiresUpdate(DbDutyStatusAvailability entityToEvaluate, DutyStatusAvailability entityToMapTo)
        {
            // Because DutyStatusAvailability is calculated on each request, updates will always be required. This method needs to be here for interface purposes. So, just return true.
            return true;
        }

        /// <inheritdoc/>
        public DbDutyStatusAvailability UpdateEntity(DbDutyStatusAvailability entityToUpdate, DutyStatusAvailability entityToMapTo)
        {
            if (entityToUpdate.DriverId != entityToMapTo.Driver.Id.ToString())
            {
                throw new ArgumentException($"Cannot update {nameof(DbDutyStatusAvailability)} '{entityToUpdate.id}' with supplied {nameof(DutyStatusAvailability)} entity because the DriverIds do not match.");
            }

            var updatedDbDutyStatusAvailability = CreateEntity(entityToMapTo);

            // Update id since id is auto-generated by the database on insert and is therefor not set by the CreateEntity method.
            updatedDbDutyStatusAvailability.id = entityToUpdate.id;
            updatedDbDutyStatusAvailability.DatabaseWriteOperationType = Common.DatabaseWriteOperationType.Update;

            return updatedDbDutyStatusAvailability;
        }
    }
}
