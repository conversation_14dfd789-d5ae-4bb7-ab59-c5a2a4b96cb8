﻿// This file is used by Code Analysis to maintain SuppressMessage
// attributes that are applied to this project.
// Project-level suppressions either have no target or are given
// a specific target and scoped to a namespace, type, member, etc.

using System.Diagnostics.CodeAnalysis;

[assembly: SuppressMessage("Style", "IDE0074:Use compound assignment", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.GeotabObjectMappers.GeotabUserDbUserObjectMapper.EntityRequiresUpdate(MyGeotabAPIAdapter.Database.Models.DbUser,Geotab.Checkmate.ObjectModel.User)~System.Boolean")]
[assembly: SuppressMessage("Style", "IDE0074:Use compound assignment", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.GeotabObjectMappers.GeotabZoneDbZoneObjectMapper.EntityRequiresUpdate(MyGeotabAPIAdapter.Database.Models.DbZone,Geotab.Checkmate.ObjectModel.Zone)~System.Boolean")]
