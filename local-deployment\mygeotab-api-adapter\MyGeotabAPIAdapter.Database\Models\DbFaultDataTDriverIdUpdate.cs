﻿using Dapper.Contrib.Extensions;
using System;

namespace MyGeotabAPIAdapter.Database.Models
{
    /// <summary>
    /// NOTE: The <see cref="id"/> property is assigned the [Key] attribute because this entity model is only intended for use in UPDATING existing database records and we want to take advantage of the speed offered by the clustered index. If this entity model were to be used for inserting new records, inserts would fail as the <see cref="id"/> column in the database is an identity column (i.e. auto-generated by the database).
    /// </summary>
    [Table("FaultDataT")]
    public class DbFaultDataTDriverIdUpdate : IDbEntity, IIdCacheableDbEntity
    {
        /// <inheritdoc/>
        [Write(false)]
        public string DatabaseTableName => "FaultDataT";

        /// <inheritdoc/>
        [Write(false)]
        public Common.DatabaseWriteOperationType DatabaseWriteOperationType { get; set; }

        /// <inheritdoc/>
        [Write(false)]
        public DateTime LastUpsertedUtc { get => RecordLastChangedUtc; }

        [Key]
        public long id { get; set; }
        public string GeotabId { get; set; }
        public long? DriverId { get; set; }
        public bool DriverIdProcessed { get; set; }
        public byte? DriverIdReason { get; set; }
        [ChangeTracker]
        public DateTime RecordLastChangedUtc { get; set; }
    }
}
