services:
  # PostgreSQL Database
  postgres:
    image: postgres:16-alpine
    container_name: postgres
    environment:
      POSTGRES_DB: geotabadapterdb
      POSTGRES_USER: pgadmin
      POSTGRES_PASSWORD: localdev123
      POSTGRES_INITDB_ARGS: "--auth-host=scram-sha-256"
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c pg_stat_statements.track=all
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c max_connections=100
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U pgadmin -d geotabadapterdb"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    networks:
      - monitoring-network
    restart: unless-stopped




  # Grafana for Advanced Dashboards
  grafana:
    image: grafana/grafana:10.2.0
    container_name: grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin123
      GF_SECURITY_ADMIN_USER: admin
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
      GF_SECURITY_ALLOW_EMBEDDING: "true"
      GF_AUTH_ANONYMOUS_ENABLED: "true"
      GF_AUTH_ANONYMOUS_ORG_ROLE: Viewer
      GF_DATABASE_TYPE: postgres
      GF_DATABASE_HOST: postgres:5432
      GF_DATABASE_NAME: geotabadapterdb
      GF_DATABASE_USER: geotabadapter_reader
      GF_DATABASE_PASSWORD: localdev123
      GF_DATABASE_SSL_MODE: disable
    ports:
      - "3000:3000"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./grafana/provisioning:/etc/grafana/provisioning
      - ./grafana/dashboards:/var/lib/grafana/dashboards
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - monitoring-network
    restart: unless-stopped







  # Dozzle - Docker Log Viewer
  dozzle:
    image: amir20/dozzle:latest
    container_name: dozzle
    environment:
      DOZZLE_LEVEL: info
      DOZZLE_TAILSIZE: 300
      DOZZLE_FILTER: "name=postgres,name=grafana,name=pghero"
      DOZZLE_NO_ANALYTICS: "true"
      DOZZLE_ENABLE_ACTIONS: "true"
    ports:
      - "8080:8080"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s
    networks:
      - monitoring-network
    restart: unless-stopped

  # PgHero - PostgreSQL Performance Dashboard
  pghero:
    image: ankane/pghero:latest
    container_name: pghero
    environment:
      DATABASE_URL: *********************************************************/geotabadapterdb
      PGHERO_USERNAME: admin
      PGHERO_PASSWORD: pghero123
      PGHERO_DATABASE_NAME: "MyGeotab Fleet DB"
    ports:
      - "8081:8080"
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8080 || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - monitoring-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  grafana_data:
    driver: local


networks:
  monitoring-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
