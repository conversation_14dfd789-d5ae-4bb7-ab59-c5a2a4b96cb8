{"name": "mygeotab-fleet-monitoring", "version": "1.0.0", "description": "MyGeotab Fleet Monitoring System using Official Geotab API Adapter with PostgreSQL and Grafana", "scripts": {"configure-mygeotab": "cd local-deployment && node configure-mygeotab.js", "start-adapter": "cd local-deployment && start-adapter.bat", "test-adapter": "cd local-deployment && node test-adapter.js", "local-deploy": "cd local-deployment && docker-compose up -d", "local-stop": "cd local-deployment && docker-compose down", "local-logs": "cd local-deployment && docker-compose logs -f", "local-reset": "cd local-deployment && docker-compose down -v && docker-compose up -d"}, "keywords": ["mygeotab", "fleet-monitoring", "telematics", "grafana", "postgresql", "docker", "official-adapter"], "license": "MIT"}