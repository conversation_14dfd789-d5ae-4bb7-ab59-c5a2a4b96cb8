@echo off
REM Enhanced MyGeotab Adapter Service Startup Script
REM Provides process isolation and monitoring capabilities

echo ========================================
echo MyGeotab Adapter Service Manager
echo ========================================

REM Check if adapter is already running
tasklist /FI "IMAGENAME eq MyGeotabAPIAdapter.exe" 2>NUL | find /I /N "MyGeotabAPIAdapter.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo [WARNING] MyGeotab Adapter is already running!
    echo.
    echo Current running processes:
    tasklist /FI "IMAGENAME eq MyGeotabAPIAdapter.exe"
    echo.
    echo To stop the adapter, run: stop-adapter-service.bat
    echo To view logs, run: npm run local-logs
    pause
    exit /b 1
)

echo [CHECK] Checking prerequisites...

REM Check if Docker services are running
docker-compose ps >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Docker Compose services not running
    echo Starting Docker services...
    docker-compose up -d
    echo [WAIT] Waiting for services to be healthy...
    timeout /t 30 /nobreak >nul
)

REM Verify database connectivity
echo [TEST] Testing database connection...
node test-adapter.js >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Database connection failed
    echo Please check Docker services and database setup
    pause
    exit /b 1
)

echo [OK] Prerequisites verified

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs

REM Start the adapter in a new window with logging
echo [START] Starting MyGeotab Adapter Service...
echo.
echo The adapter will run in a separate window.
echo To monitor:
echo   - Logs: npm run local-logs
echo   - Grafana: http://localhost:3000
echo   - PgHero: http://localhost:8081 (admin/pghero123)
echo   - Dozzle: http://localhost:8080
echo.

REM Start adapter in new window with title and logging
start "MyGeotab Adapter Service" /D "official-adapter\MyGeotabAPIAdapter_SCD_win-x64" cmd /k "echo MyGeotab Adapter Service Started & echo Press Ctrl+C to stop & MyGeotabAPIAdapter.exe"

echo [OK] MyGeotab Adapter Service started successfully!
echo.
echo [MONITOR] Monitoring URLs:
echo   - Grafana Dashboard: http://localhost:3000
echo   - Database Monitor: http://localhost:8081 (admin/pghero123)
echo   - Container Logs: http://localhost:8080
echo.
echo [MANAGE] Management Commands:
echo   - Stop adapter: stop-adapter-service.bat
echo   - View logs: npm run local-logs
echo   - Test connection: npm run test-adapter
echo.
echo The adapter is now running in a separate window.
echo You can safely close this window.
pause
