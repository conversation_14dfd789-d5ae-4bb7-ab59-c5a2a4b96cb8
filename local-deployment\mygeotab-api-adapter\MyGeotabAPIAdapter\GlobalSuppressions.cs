﻿// This file is used by Code Analysis to maintain SuppressMessage
// attributes that are applied to this project.
// Project-level suppressions either have no target or are given
// a specific target and scoped to a namespace, type, member, etc.

using System.Diagnostics.CodeAnalysis;

[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DVIRLogManipulator.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Add_Ons.VSS.OVDSClientWorker.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Usage", "CA2219:Do not raise exceptions in finally clauses", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DVIRLogManipulator.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.DVIRLogProcessor.GetDbDVIRDefectAsync(Geotab.Checkmate.ObjectModel.DVIRDefect,System.Threading.CancellationTokenSource)~System.Threading.Tasks.Task{MyGeotabAPIAdapter.Database.Models.DbDVIRDefect}")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.DVIRLogProcessor.GetDbDVIRDefectRemarkAsync(Geotab.Checkmate.ObjectModel.DefectRemark,System.Threading.CancellationTokenSource)~System.Threading.Tasks.Task{MyGeotabAPIAdapter.Database.Models.DbDVIRDefectRemark}")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.DutyStatusAvailabilityProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.Orchestrator.ValidateMyGeotabVersionInformationAsync~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.Orchestrator.WaitForConnectivityRestorationAsync~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0074:Use compound assignment", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DVIRLogManipulator.AddDefectRemark(Geotab.Checkmate.ObjectModel.DVIRDefect,MyGeotabAPIAdapter.Database.Models.DbDVIRDefectUpdate)~System.Boolean")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.ControllerProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.DeviceProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.DiagnosticProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.FailureModeProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.RuleProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.UserProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.ZoneProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.ZoneTypeProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0060:Remove unused parameter", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.DeviceStatusInfoProcessor.InitializeOrUpdateCachesAsync(System.Threading.CancellationTokenSource)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0060:Remove unused parameter", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.DutyStatusAvailabilityProcessor.InitializeOrUpdateCachesAsync(System.Threading.CancellationTokenSource)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Add_Ons.VSS.OVDSClientWorker.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DVIRLogManipulator.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.BinaryDataProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.BinaryDataProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.ControllerProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.DeviceProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.DeviceStatusInfoProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.DeviceStatusInfoProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.DiagnosticProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.DriverChangeProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.DriverChangeProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.DutyStatusAvailabilityProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.DVIRLogProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.DVIRLogProcessor.GetDbDVIRDefectRemarksAsync(System.String,System.Threading.CancellationTokenSource)~System.Threading.Tasks.Task{System.Collections.Generic.List{MyGeotabAPIAdapter.Database.Models.DbDVIRDefectRemark}}")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.DVIRLogProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.ExceptionEventProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.ExceptionEventProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.FailureModeProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.FaultDataProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.FaultDataProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.GroupProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.LogRecordProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.LogRecordProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.RuleProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.StatusDataProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.StatusDataProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.TripProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.TripProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.UnitOfMeasureProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.UserProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.ZoneProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Services.ZoneTypeProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.ServiceTracker.InitializeDbOServiceTrackingListAsync~System.Threading.Tasks.Task")]
