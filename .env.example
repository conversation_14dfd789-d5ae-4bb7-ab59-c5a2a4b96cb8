# =============================================================================
# PostgreSQL Monitoring System - Environment Configuration
# =============================================================================

# -----------------------------------------------------------------------------
# REQUIRED VARIABLES - These must be set for the application to function
# -----------------------------------------------------------------------------

# Database Configuration (REQUIRED)
# PostgreSQL server hostname - set automatically by ARM template deployment
DB_HOST=your-postgres-server.postgres.database.azure.com
# PostgreSQL server port - standard port is 5432
DB_PORT=5432
# Database name for monitoring data storage - default is monitoring_db
DB_NAME=monitoring_db
# PostgreSQL administrator username - set during deployment
DB_USER=your-username
# PostgreSQL administrator password - must be secure, min 8 chars with mixed case and numbers
DB_PASSWORD=your-password

# Application Configuration (REQUIRED)
NODE_ENV=production
PORT=3000

# -----------------------------------------------------------------------------
# OPTIONAL VARIABLES - These enhance functionality but are not required
# -----------------------------------------------------------------------------

# Logging Configuration (Optional - defaults to 'info')
LOG_LEVEL=info

# Dashboard URL (Optional - used in alert notifications)
# If not set, defaults to http://localhost:3000/dashboard
DASHBOARD_URL=https://your-app.azurewebsites.net/dashboard

# -----------------------------------------------------------------------------
# NOTIFICATION CHANNELS - All optional, enable as needed
# -----------------------------------------------------------------------------

# Email Alert Configuration (Optional)
# Required for email notifications: SMTP_HOST, SMTP_USER, SMTP_PASSWORD, ALERT_EMAIL_TO
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM=<EMAIL>
ALERT_EMAIL_TO=<EMAIL>

# Webhook Configuration (Optional)
# Set to enable custom webhook notifications
WEBHOOK_URL=https://your-webhook-endpoint.com/alerts

# Slack Integration (Optional)
# Get webhook URL from Slack app configuration
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK

# Microsoft Teams Integration (Optional)
# Get webhook URL from Teams channel connector
TEAMS_WEBHOOK_URL=https://your-tenant.webhook.office.com/webhookb2/YOUR-TEAMS-WEBHOOK

# -----------------------------------------------------------------------------
# AZURE SPECIFIC VARIABLES - Set automatically by ARM template
# -----------------------------------------------------------------------------

# Application Insights (Optional - for Azure monitoring)
APPINSIGHTS_INSTRUMENTATIONKEY=your-instrumentation-key

# Azure Resource Information (Optional - for management)
AZURE_RESOURCE_GROUP=your-resource-group
AZURE_SUBSCRIPTION_ID=your-subscription-id

# Azure AD Authentication (Optional - for enterprise SSO and role-based access)
# Azure AD tenant ID - GUID format, found in Azure portal under tenant properties
AZURE_AD_TENANT_ID=your-tenant-id
# Azure AD application client ID - GUID format, from app registration
AZURE_AD_CLIENT_ID=your-client-id
# Azure AD application client secret - secure string from app registration, keep confidential
AZURE_AD_CLIENT_SECRET=your-client-secret
# Azure AD redirect URI - callback URL for authentication flow, auto-generated from app URL
AZURE_AD_REDIRECT_URI=https://your-app.azurewebsites.net/auth/azuread/callback

# Session Configuration (Required for authentication and security)
# Session secret for secure user sessions - must be long random string, auto-generated in deployment
SESSION_SECRET=your-session-secret-change-in-production-use-long-random-string
# Cookie encryption key for secure cookie storage - exactly 32 characters, auto-generated
COOKIE_ENCRYPTION_KEY=your-cookie-encryption-key-32-chars
# Cookie encryption initialization vector - exactly 16 characters, auto-generated
COOKIE_ENCRYPTION_IV=your-cookie-encryption-iv-16-chars

# -----------------------------------------------------------------------------
# GRAFANA CONFIGURATION - Set when Grafana is enabled
# -----------------------------------------------------------------------------

# Grafana Connection (Optional - set by ARM template when Grafana enabled)
GRAFANA_URL=http://your-grafana-container.australiaeast.azurecontainer.io:3000
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=your-grafana-password

# Grafana Azure AD Integration (Optional - for Grafana SSO)
GF_AUTH_AZUREAD_ENABLED=true
GF_AUTH_AZUREAD_CLIENT_ID=your-grafana-client-id
GF_AUTH_AZUREAD_CLIENT_SECRET=your-grafana-client-secret
GF_AUTH_AZUREAD_SCOPES=openid email profile
GF_AUTH_AZUREAD_AUTH_URL=https://login.microsoftonline.com/your-tenant-id/oauth2/v2.0/authorize
GF_AUTH_AZUREAD_TOKEN_URL=https://login.microsoftonline.com/your-tenant-id/oauth2/v2.0/token
GF_AUTH_AZUREAD_ALLOWED_DOMAINS=yourcompany.com
GF_AUTH_AZUREAD_ROLE_ATTRIBUTE_PATH=contains(roles[*], 'Admin') && 'Admin' || contains(roles[*], 'Editor') && 'Editor' || 'Viewer'

# Grafana Database Configuration (Set automatically by ARM template)
GF_DATABASE_TYPE=postgres
GF_DATABASE_HOST=your-postgres-server.postgres.database.azure.com:5432
GF_DATABASE_NAME=grafana
GF_DATABASE_USER=pgadmin
GF_DATABASE_PASSWORD=your-password
GF_DATABASE_SSL_MODE=require

# -----------------------------------------------------------------------------
# ADVANCED CONFIGURATION - Usually not needed to change
# -----------------------------------------------------------------------------

# CORS Configuration (Optional - defaults to allow all origins)
ALLOWED_ORIGINS=https://yourdomain.com,https://anotherdomain.com

# Rate Limiting (Optional - defaults shown)
# RATE_LIMIT_WINDOW_MS=900000
# RATE_LIMIT_MAX_REQUESTS=100

# Monitoring Configuration (Optional - defaults shown)
# METRICS_COLLECTION_INTERVAL=60000
# ALERT_CHECK_INTERVAL=60000
# DATA_RETENTION_DAYS=7
