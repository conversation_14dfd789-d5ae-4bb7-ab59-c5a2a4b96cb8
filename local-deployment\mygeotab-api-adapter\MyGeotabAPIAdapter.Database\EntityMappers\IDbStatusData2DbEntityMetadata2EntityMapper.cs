﻿using MyGeotabAPIAdapter.Database.Models;


namespace MyGeotabAPIAdapter.Database.EntityMappers
{
    /// <summary>
    /// Interface for a class with methods involving mapping between <see cref="DbStatusData2"/> and <see cref="DbEntityMetadata2"/> entities.
    /// </summary>
    public interface IDbStatusData2DbEntityMetadata2EntityMapper : ICreateOnlyEntityMapper<DbStatusData2, DbEntityMetadata2>
    {
    }
}
