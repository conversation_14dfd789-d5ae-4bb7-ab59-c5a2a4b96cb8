# 🚗 MyGeotab Fleet Monitoring System

A production-ready fleet monitoring solution using the **Official Geotab MyGeotab API Adapter** with real-time data integration, PostgreSQL storage, and Grafana dashboards.

## 🏗️ **System Architecture**

| Service | Port | Purpose | Technology |
|---------|------|---------|------------|
| **Configuration Center** | 4000 | MyGeotab setup wizard | Node.js |
| **Grafana Analytics** | 3000 | Fleet dashboards & alerting | Grafana 10.2.0 |
| **PgHero Performance** | 8081 | Database monitoring | Ruby/Rails |
| **Container Logs** | 8080 | System log monitoring | Dozzle |
| **PostgreSQL Database** | 5432 | Fleet data storage | PostgreSQL 15 |
| **Redis Cache** | 6379 | Performance caching | Redis 7 |
| **Official MyGeotab Adapter** | - | Data synchronization | .NET 9.0 (Windows) |

## 🚀 **Key Features**

### **Official MyGeotab Integration**
- ✅ Official Geotab MyGeotab API Adapter v3.8.0
- ✅ Real-time data feeds (30-second intervals)
- ✅ Complete official database schema
- ✅ Enterprise-grade reliability and support
- ✅ GPS tracking, diagnostics, trips, and fault data

### **Monitoring & Analytics**
- ✅ Real-time Grafana dashboards
- ✅ PostgreSQL performance monitoring with PgHero
- ✅ Container log management with Dozzle
- ✅ Redis caching for optimal performance
- ✅ Health monitoring and alerting

### **Easy Setup**
- ✅ Web-based configuration wizard
- ✅ Docker Compose infrastructure
- ✅ Automated database schema creation
- ✅ One-command deployment

## 🏗️ Data Flow

```
MyGeotab API → Official MyGeotab Adapter → PostgreSQL → Grafana
                                        ↗
                                   Redis Cache
```

### Key Components

- **Official MyGeotab API Adapter**: Geotab's official .NET adapter for data synchronization
- **PostgreSQL Database**: Stores fleet data using official Geotab schema
- **Grafana**: Provides dashboards and alerting for fleet monitoring
- **Redis**: Caches data for improved performance
- **Configuration Service**: Web-based setup wizard for MyGeotab credentials

## 🚀 **Quick Start**

**Get running in 5 minutes!** See [QUICK-START.md](QUICK-START.md) for the complete guide.

### **1. Deploy Infrastructure**
```bash
cd local-deployment && docker-compose up -d
```

### **2. Configure MyGeotab**
```bash
npm run configure-mygeotab
```

### **3. Download & Start Official Adapter**
```bash
# Download the official adapter from Geotab
# Extract to local-deployment/official-adapter/
npm run start-adapter
```

### **4. Access Services**
- **Grafana**: http://localhost:3000 (admin/admin)
- **Database Monitoring**: http://localhost:8081
- **Container Logs**: http://localhost:8080

**Requirements**: Docker Desktop, Windows (for adapter), MyGeotab credentials.
**Setup Time**: 5 minutes for complete deployment.

## 📊 **What You Get**

### **Real-time Fleet Data**
- Vehicle locations and status
- Trip data and analytics
- Driver performance metrics
- Fault codes and diagnostics
- Engine health monitoring
- Geofencing and routes

### **Official Database Schema**
- Complete Geotab schema with 20+ tables
- LogRecords2 - GPS tracking data
- StatusData2 - Engine diagnostics
- FaultData2 - Fault codes and diagnostics
- Trips2 - Trip information
- Devices2 - Vehicle information
- Users2 - Driver information

### **Monitoring Stack**
- Real-time Grafana dashboards
- PostgreSQL performance monitoring
- Container log management
- Redis caching for performance
- Health monitoring and alerting

## 📋 **Prerequisites**

- **Docker Desktop** installed and running
- **Windows environment** (for MyGeotab API Adapter)
- **MyGeotab account** with API access
- **Available ports**: 3000, 4000, 5432, 6379, 8080, 8081

## 🔧 **Commands**

```bash
# Deploy infrastructure
cd local-deployment
docker-compose up -d

# Configure MyGeotab credentials
npm run configure-mygeotab

# Start official adapter
npm run start-adapter

# Test adapter connection
npm run test-adapter

# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Reset everything (delete data)
docker-compose down -v
```

## 🔍 **Troubleshooting**

### **MyGeotab Not Connecting**
1. Check credentials at http://localhost:4000
2. Verify MyGeotab server URL (e.g., my.geotab.com)
3. Ensure MyGeotab account has API access
4. Check Windows firewall settings

### **No Data in Database**
1. Verify MyGeotab adapter is running
2. Wait 2-3 minutes for first sync
3. Check adapter logs for errors
4. Verify database connection

### **Grafana Can't Connect**
1. Use `postgres` not `localhost` as host
2. Check database credentials: pgadmin/localdev123
3. Verify PostgreSQL container is running

### **Reset Everything**
```bash
# Stop and delete all data
docker-compose down -v

# Start fresh
docker-compose up -d
```

## 📊 **Database Schema**

The official MyGeotab API Adapter creates a complete database schema with tables including:

- **LogRecords2** - GPS tracking data with timestamps, coordinates, speed
- **StatusData2** - Engine diagnostics and sensor data
- **FaultData2** - Fault codes and diagnostic trouble codes
- **Trips2** - Trip information with start/end times and distances
- **Devices2** - Vehicle information including make, model, VIN
- **Users2** - Driver information and assignments
- **Plus 20+ additional tables** for comprehensive fleet data

## 🔐 **Security Notes**

- All services run on localhost only
- MyGeotab credentials stored in adapter configuration
- Database uses development credentials (change for production)
- No external network exposure by default
- Official adapter provides enterprise-grade security

## 📚 **Additional Resources**

- **Official MyGeotab API Adapter**: [GitHub Repository](https://github.com/Geotab/mygeotab-api-adapter)
- **MyGeotab API Documentation**: [Geotab Developer Portal](https://developers.geotab.com/)
- **Grafana Documentation**: [Grafana Docs](https://grafana.com/docs/)

## 🤝 **Support**

For support and questions:
- Check the troubleshooting section above
- Review the official MyGeotab API Adapter documentation
- Create an issue in the repository for bugs or feature requests

## 📄 **License**

MIT License - see LICENSE file for details

---

**🚛 Your official MyGeotab fleet monitoring system is ready!**

Start with `cd local-deployment && docker-compose up -d` then configure your MyGeotab credentials.
