﻿using Microsoft.Data.SqlClient;
using NLog;
using Npgsql;
using System;
using System.Data.Common;
using System.Reflection;
using System.Threading.Tasks;

namespace MyGeotabAPIAdapter.Database.DataAccess
{
    /// <summary>
    /// A class designed to wrap transactions associated with database CRUD operations.
    /// </summary>
    public class UnitOfWork : IDisposable
    {
        readonly Logger logger = LogManager.GetCurrentClassLogger();
        NpgsqlConnection npgsqlConnection;
        SqlConnection sqlConnection;

        /// <summary>
        /// The <see cref="DbConnection"/> associated with the <see cref="UnitOfWork"/> instance.
        /// </summary>
        public DbConnection Connection { get; }

        /// <summary>
        /// A unique identifier assigned to the <see cref="UnitOfWork"/> at the time of its creation. Intended for debugging purposes.
        /// </summary>
        public string Id { get; private set; }

        /// <summary>
        /// Indicates whether the <see cref="UnitOfWork"/> instance has been disposed.
        /// </summary>
        public bool IsDisposed { get; private set; } = false;

        /// <summary>
        /// The <see cref="Npgsql.NpgsqlConnection"/> associated with the <see cref="UnitOfWork"/> instance. Intended for use with PostgreSQL bulk operations.
        /// </summary>
        public NpgsqlConnection NpgsqlConnection
        {
            get => npgsqlConnection;
            internal set
            {
                if (npgsqlConnection != null)
                {
                    throw new InvalidOperationException("Cannot set the NpgsqlConnection because it has already been set.");
                }
                npgsqlConnection = value;
                NpgsqlTransaction = npgsqlConnection.BeginTransaction();
            }
        }

        /// <summary>
        /// The <see cref="Npgsql.NpgsqlTransaction"/> associated with the <see cref="UnitOfWork"/> instance. Intended for use with PostgreSQL bulk operations.
        /// </summary>
        public NpgsqlTransaction NpgsqlTransaction { get; private set; }

        /// <summary>
        /// The <see cref="Microsoft.Data.SqlClient.SqlConnection"/> associated with the <see cref="UnitOfWork"/> instance. Intended for use with SQL Server bulk operations.
        /// </summary>
        public SqlConnection SqlConnection 
        { 
            get => sqlConnection;
            internal set 
            {
                if (sqlConnection != null)
                {
                    throw new InvalidOperationException("Cannot set the SqlConnection because it has already been set.");
                }
                sqlConnection = value;
                SqlTransaction = sqlConnection.BeginTransaction();
            }
        }

        /// <summary>
        /// The <see cref="Microsoft.Data.SqlClient.SqlTransaction"/> associated with the <see cref="UnitOfWork"/> instance. Intended for use with SQL Server bulk operations.
        /// </summary>
        public SqlTransaction SqlTransaction { get; private set; }

        /// <summary>
        /// The maximum number of seconds that a database <see cref="Task"/> or batch thereof can take to be completed before it is deemed that there is a database connectivity issue and a <see cref="Database.DatabaseConnectionException"/> should be thrown.
        /// </summary>
        public int TimeoutSecondsForDatabaseTasks { get; }

        /// <summary>
        /// The <see cref="DbTransaction"/> associated with the <see cref="UnitOfWork"/> instance.
        /// </summary>
        public DbTransaction Transaction { get; private set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="UnitOfWork"/> class and starts a database transaction on the <paramref name="connection"/>. Should be called by the <see cref="IUnitOfWorkContext.CreateUnitOfWork(Databases, int, DatabaseConnectionType)"/> method.
        /// </summary>
        /// <param name="connection">An open <see cref="DbConnection"/> to be associated with the <see cref="UnitOfWork"/> instance.</param>
        /// <param name="timeoutSecondsForDatabaseTasks">The maximum number of seconds that a database <see cref="System.Threading.Tasks.Task"/> or batch thereof can take to be completed before it is deemed that there is a database connectivity issue and a <see cref="Database.DatabaseConnectionException"/> should be thrown.</param>
        public UnitOfWork(DbConnection connection, int timeoutSecondsForDatabaseTasks)
        {
            Id = System.Guid.NewGuid().ToString();
            Connection = connection;
            TimeoutSecondsForDatabaseTasks = timeoutSecondsForDatabaseTasks;
            Transaction = Connection.BeginTransaction();
            logger.Trace($"{nameof(UnitOfWork)} [Id: {Id}] created and database transaction initiated.");
        }

        /// <summary>
        /// Disposes the current <see cref="UnitOfWork"/> instance.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes the <see cref="Transaction"/> associated with the current <see cref="UnitOfWork"/> instance.
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                if (Transaction != null)
                {
                    Transaction.Dispose();
                    logger.Trace($"{nameof(UnitOfWork)} [Id: {Id}] database transaction disposed.");
                }

                if (Connection != null && Connection.State == System.Data.ConnectionState.Open)
                {
                    Connection.Close();
                    Connection.Dispose();
                }

                if (NpgsqlTransaction != null)
                { 
                    NpgsqlTransaction.Dispose();
                    logger.Trace($"{nameof(UnitOfWork)} [Id: {Id}] PostgreSQL database transaction disposed.");
                }

                if (NpgsqlConnection != null && NpgsqlConnection.State == System.Data.ConnectionState.Open)
                {
                    NpgsqlConnection.Close();
                    Connection.Dispose();
                }

                if (SqlTransaction != null)
                { 
                    SqlTransaction.Dispose();
                    logger.Trace($"{nameof(UnitOfWork)} [Id: {Id}] SQL Server database transaction disposed.");
                }

                if (SqlConnection != null && SqlConnection.State == System.Data.ConnectionState.Open)
                { 
                    SqlConnection.Close();
                    SqlConnection.Dispose();
                }

                var id = Id;
                Id = string.Empty;
                IsDisposed = true;
                logger.Trace($"{nameof(UnitOfWork)} [Id: {id}] disposed.");
            }
        }

        /// <summary>
        /// Asynchronously commits the <see cref="Transaction"/>.
        /// </summary>
        /// <returns></returns>
        public async Task CommitAsync()
        {
            if (Transaction != null)
            {
                await Transaction.CommitAsync();
                logger.Debug($"{nameof(UnitOfWork)} [Id: {Id}] database transaction committed.");
            }

            if (NpgsqlTransaction != null)
            { 
                await NpgsqlTransaction.CommitAsync();
                logger.Debug($"{nameof(UnitOfWork)} [Id: {Id}] PostgreSQL database transaction committed.");
            }

            if (SqlTransaction != null)
            { 
                await SqlTransaction.CommitAsync();
                logger.Debug($"{nameof(UnitOfWork)} [Id: {Id}] SQL Server database transaction committed.");
            }
        }

        /// <summary>
        /// Rolls back the <see cref="Transaction"/> from a pending state.
        /// </summary>
        /// <returns></returns>
        public async Task RollBackAsync()
        {
            if (Transaction != null)
            {
                await Transaction.RollbackAsync();
                logger.Debug($"{nameof(UnitOfWork)} [Id: {Id}] database transaction rolled back.");
            }

            if (NpgsqlTransaction != null)
            {
                await NpgsqlTransaction.RollbackAsync();
                logger.Debug($"{nameof(UnitOfWork)} [Id: {Id}] PostgreSQL database transaction rolled back.");
            }

            if (SqlTransaction != null)
            {
                await SqlTransaction.RollbackAsync();
                logger.Debug($"{nameof(UnitOfWork)} [Id: {Id}] SQL Server database transaction rolled back.");
            }
        }
    }
}
