{"name": "mygeotab-local-deployment", "version": "1.0.0", "description": "Local deployment for MyGeotab Fleet Monitoring using Official Geotab API Adapter", "scripts": {"configure-mygeotab": "node configure-mygeotab.js", "test-adapter": "node test-adapter.js", "test-database-users": "node test-database-users.js", "start-adapter": "start-adapter.bat", "start-adapter-unix": "bash start-adapter.sh", "start-adapter-service": "start-adapter-service.bat", "stop-adapter-service": "stop-adapter-service.bat", "restart-adapter": "stop-adapter-service.bat && timeout /t 5 /nobreak && start-adapter-service.bat", "system-status": "system-status.bat", "monitor-adapter": "monitor-adapter.bat", "quick-check": "node test-adapter.js && system-status.bat", "full-deploy": "docker-compose up -d && timeout /t 30 /nobreak && start-adapter-service.bat", "logs": "docker-compose logs -f", "up": "docker-compose up -d", "down": "docker-compose down", "reset": "docker-compose down -v && docker-compose up -d", "health-check": "docker-compose ps && system-status.bat", "quick-setup": "quick-setup.bat", "first-time-setup": "first-time-setup.bat", "performance": "performance-monitor.bat", "setup-database": "setup-database-wrapper.bat", "setup-monitoring": "setup-monitoring.bat", "monitoring-up": "docker-compose -f docker-compose.monitoring.yml up -d", "monitoring-down": "docker-compose -f docker-compose.monitoring.yml down", "monitoring-logs": "docker-compose -f docker-compose.monitoring.yml logs -f", "monitoring-status": "docker-compose -f docker-compose.monitoring.yml ps && echo. && echo Checking service health... && curl -f http://localhost:19999/api/v1/info >nul 2>&1 && echo ✅ Netdata: http://localhost:19999 || echo ❌ Netdata not ready && curl -f http://localhost:3001 >nul 2>&1 && echo ✅ Uptime Kuma: http://localhost:3001 || echo ❌ Uptime Kuma not ready && curl -f http://localhost:3100/ready >nul 2>&1 && echo ✅ Loki: http://localhost:3100 || echo ❌ Loki not ready && curl -f http://localhost:5050/misc/ping >nul 2>&1 && echo ✅ pgAdmin: http://localhost:5050 || echo ❌ pgAdmin not ready", "monitoring-restart": "docker-compose -f docker-compose.monitoring.yml restart", "monitoring-stop": "docker-compose -f docker-compose.monitoring.yml stop", "monitoring-reset": "docker-compose -f docker-compose.monitoring.yml down -v && docker-compose -f docker-compose.monitoring.yml up -d"}, "dependencies": {"pg": "^8.16.1"}, "license": "MIT"}