{"name": "mygeotab-local-deployment", "version": "1.0.0", "description": "Local deployment for MyGeotab Fleet Monitoring using Official Geotab API Adapter", "scripts": {"configure-mygeotab": "node configure-mygeotab.js", "test-adapter": "node test-adapter.js", "test-database-users": "node test-database-users.js", "start-adapter": "start-adapter.bat", "start-adapter-unix": "bash start-adapter.sh", "start-adapter-service": "start-adapter-service.bat", "stop-adapter-service": "stop-adapter-service.bat", "restart-adapter": "stop-adapter-service.bat && timeout /t 5 /nobreak && start-adapter-service.bat", "system-status": "system-status.bat", "monitor-adapter": "monitor-adapter.bat", "quick-check": "node test-adapter.js && system-status.bat", "full-deploy": "docker-compose up -d && timeout /t 30 /nobreak && start-adapter-service.bat", "logs": "docker-compose logs -f", "up": "docker-compose up -d", "down": "docker-compose down", "reset": "docker-compose down -v && docker-compose up -d", "health-check": "docker-compose ps && system-status.bat", "quick-setup": "quick-setup.bat", "first-time-setup": "first-time-setup.bat", "performance": "performance-monitor.bat", "setup-database": "setup-database-wrapper.bat"}, "dependencies": {"pg": "^8.16.1"}, "license": "MIT"}