﻿// This file is used by Code Analysis to maintain SuppressMessage
// attributes that are applied to this project.
// Project-level suppressions either have no target or are given
// a specific target and scoped to a namespace, type, member, etc.

using System.Diagnostics.CodeAnalysis;

[assembly: SuppressMessage("Style", "IDE0074:Use compound assignment", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Database.EntityPersisters.GenericEntityPersister`1.BulkDeleteSqlServerAsync(MyGeotabAPIAdapter.Database.DataAccess.IDatabaseUnitOfWorkContext,System.Collections.Generic.IEnumerable{`0},System.String)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0074:Use compound assignment", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Database.EntityPersisters.GenericEntityPersister`1.BulkInsertSqlServerAsync(MyGeotabAPIAdapter.Database.DataAccess.IDatabaseUnitOfWorkContext,System.Collections.Generic.IEnumerable{`0},System.String)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0074:Use compound assignment", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.Database.EntityPersisters.GenericEntityPersister`1.BulkUpdateSqlServerAsync(MyGeotabAPIAdapter.Database.DataAccess.IDatabaseUnitOfWorkContext,System.Collections.Generic.IEnumerable{`0},System.String)~System.Threading.Tasks.Task")]
