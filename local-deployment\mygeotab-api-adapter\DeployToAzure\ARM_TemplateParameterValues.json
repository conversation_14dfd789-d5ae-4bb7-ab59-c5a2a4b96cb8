{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"versionOfMyGeotabAPIAdapter": {"value": "<versionOfMyGeotabAPIAdapter>"}, "location": {"value": "<location>"}, "vnetName": {"value": "vnetMYGApiAdapter"}, "vmName": {"value": "vmMYGApiAdapter"}, "vmSize": {"value": "Standard_D4d_v4"}, "vmUsername": {"value": "<vm admin user name>"}, "vmPassword": {"value": "<vm admin user password>"}, "nsgSourceAddressPrefix": {"value": "**********/24"}, "sqlServerName": {"value": "sqlServerMYGApiAdapter"}, "sqlAdminUsername": {"value": "<sql server admin user name>"}, "sqlAdminPassword": {"value": "<sql server admin password>"}, "sqlUseDataModel2": {"value": "false"}, "sqlDatabaseName": {"value": "geotabadapterdb"}, "sqlDatabaseServiceTier": {"value": "Standard"}, "sqlDatabaseComputeSize": {"value": "S1"}, "scriptFilename": {"value": "MyGeotabAPIAdapterDeploymentScript.ps1"}, "rootPath": {"value": "C:\\Geotab"}, "appUser": {"value": "geotabadapter_client"}, "appPassword": {"value": "<app password>"}, "loginMYGServer": {"value": "my.geotab.com"}, "loginMYGDatabase": {"value": "<MyGeotabDatabase>"}, "loginMYGUser": {"value": "<MyGeotabUser>"}, "loginMYGPassword": {"value": "<MyGeotabPassword>"}, "FeedStartOption": {"value": "<FeedStartOption>"}, "FeedStartSpecificTimeUTC": {"value": "<FeedStartSpecificTimeUTC>"}, "StartAPIAdapterWhenDeployed": {"value": "false"}}}