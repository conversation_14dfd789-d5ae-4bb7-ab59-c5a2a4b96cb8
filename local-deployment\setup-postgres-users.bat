@echo off
REM Run this script from the local-deployment directory to set up Postgres users, permissions, and schema

echo Copying setup-postgres-users.sql into the postgres container...
docker cp setup-postgres-users.sql postgres:/setup-postgres-users.sql

echo Running user/permission setup script inside the postgres container...
docker exec -i postgres psql -U pgadmin -d postgres -f /setup-postgres-users.sql

echo Cleaning up user/permission script from container...
docker exec postgres rm /setup-postgres-users.sql

REM === SCHEMA CREATION ===
echo Copying PG_CumulativeSchemaCreation.sql into the postgres container...
docker cp mygeotab-api-adapter/MyGeotabAPIAdapter/Scripts/PostgreSQL/v2/PG_CumulativeSchemaCreation.sql postgres:/PG_CumulativeSchemaCreation.sql

echo Running schema creation script in geotabadapterdb...
docker exec -i postgres psql -U pgadmin -d geotabadapterdb -f /PG_CumulativeSchemaCreation.sql

echo Cleaning up schema script from container...
docker exec postgres rm /PG_CumulativeSchemaCreation.sql

REM === PARTITIONING ===
echo Copying PG_0.0.0.1_spManagePartitions.sql into the postgres container...
docker cp mygeotab-api-adapter/MyGeotabAPIAdapter/Scripts/PostgreSQL/v2/PG_0.0.0.1_spManagePartitions.sql postgres:/PG_0.0.0.1_spManagePartitions.sql

echo Running partitioning script in geotabadapterdb...
docker exec -i postgres psql -U pgadmin -d geotabadapterdb -f /PG_0.0.0.1_spManagePartitions.sql

echo Cleaning up partitioning script from container...
docker exec postgres rm /PG_0.0.0.1_spManagePartitions.sql

REM === EXECUTE PARTITIONING FUNCTION ===
echo Executing spManagePartitions to initialize DB partitions...
docker exec -i postgres psql -U pgadmin -d geotabadapterdb -c "SELECT public.\"spManagePartitions\"(CURRENT_TIMESTAMP AT TIME ZONE 'UTC', 'monthly');"

echo Postgres setup, schema creation, and partitioning complete.

REM === VERIFICATION ===
echo Verifying database user setup...
node test-database-users.js
if %ERRORLEVEL% neq 0 (
    echo [ERROR] Database user verification failed
    exit /b 1
)

echo [OK] Database setup completed successfully!