{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Tests\\MyGeotabAPIAdapter.Tests.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Configuration\\MyGeotabAPIAdapter.Configuration.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Configuration\\MyGeotabAPIAdapter.Configuration.csproj", "projectName": "MyGeotabAPIAdapter.Configuration", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Configuration\\MyGeotabAPIAdapter.Configuration.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Configuration\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Helpers\\MyGeotabAPIAdapter.Helpers.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Helpers\\MyGeotabAPIAdapter.Helpers.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.NETCore.Platforms": {"target": "Package", "version": "[7.0.4, )"}, "NLog.Extensions.Logging": {"target": "Package", "version": "[5.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database.EntityPersisters\\MyGeotabAPIAdapter.Database.EntityPersisters.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database.EntityPersisters\\MyGeotabAPIAdapter.Database.EntityPersisters.csproj", "projectName": "MyGeotabAPIAdapter.Database.EntityPersisters", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database.EntityPersisters\\MyGeotabAPIAdapter.Database.EntityPersisters.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database.EntityPersisters\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Configuration\\MyGeotabAPIAdapter.Configuration.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Configuration\\MyGeotabAPIAdapter.Configuration.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database\\MyGeotabAPIAdapter.Database.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database\\MyGeotabAPIAdapter.Database.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Logging\\MyGeotabAPIAdapter.Logging.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Logging\\MyGeotabAPIAdapter.Logging.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"NLog.Extensions.Logging": {"target": "Package", "version": "[5.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database\\MyGeotabAPIAdapter.Database.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database\\MyGeotabAPIAdapter.Database.csproj", "projectName": "MyGeotabAPIAdapter.Database", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database\\MyGeotabAPIAdapter.Database.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Configuration\\MyGeotabAPIAdapter.Configuration.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Configuration\\MyGeotabAPIAdapter.Configuration.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Helpers\\MyGeotabAPIAdapter.Helpers.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Helpers\\MyGeotabAPIAdapter.Helpers.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Logging\\MyGeotabAPIAdapter.Logging.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Logging\\MyGeotabAPIAdapter.Logging.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.MyGeotabAPI\\MyGeotabAPIAdapter.MyGeotabAPI.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.MyGeotabAPI\\MyGeotabAPIAdapter.MyGeotabAPI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Dapper": {"target": "Package", "version": "[2.1.66, )"}, "FastMember": {"target": "Package", "version": "[1.5.0, )"}, "Microsoft.Data.SqlClient": {"target": "Package", "version": "[6.0.2, )"}, "Microsoft.VisualStudio.Threading": {"target": "Package", "version": "[17.14.15, )"}, "NLog.Extensions.Logging": {"target": "Package", "version": "[5.5.0, )"}, "Npgsql": {"target": "Package", "version": "[9.0.3, )"}, "Oracle.ManagedDataAccess.Core": {"target": "Package", "version": "[23.8.0, )"}, "Polly": {"target": "Package", "version": "[8.5.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.DataOptimizer\\MyGeotabAPIAdapter.DataOptimizer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.DataOptimizer\\MyGeotabAPIAdapter.DataOptimizer.csproj", "projectName": "MyGeotabAPIAdapter.DataOptimizer", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.DataOptimizer\\MyGeotabAPIAdapter.DataOptimizer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.DataOptimizer\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Configuration\\MyGeotabAPIAdapter.Configuration.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Configuration\\MyGeotabAPIAdapter.Configuration.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database.EntityPersisters\\MyGeotabAPIAdapter.Database.EntityPersisters.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database.EntityPersisters\\MyGeotabAPIAdapter.Database.EntityPersisters.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database\\MyGeotabAPIAdapter.Database.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database\\MyGeotabAPIAdapter.Database.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Geospatial\\MyGeotabAPIAdapter.Geospatial.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Geospatial\\MyGeotabAPIAdapter.Geospatial.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Helpers\\MyGeotabAPIAdapter.Helpers.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Helpers\\MyGeotabAPIAdapter.Helpers.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Logging\\MyGeotabAPIAdapter.Logging.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Logging\\MyGeotabAPIAdapter.Logging.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.NETCore.Platforms": {"target": "Package", "version": "[7.0.4, )"}, "NLog.Extensions.Logging": {"target": "Package", "version": "[5.5.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-x64", "version": "[9.0.6, 9.0.6]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[9.0.6, 9.0.6]"}, {"name": "Microsoft.NETCore.App.Host.linux-x64", "version": "[9.0.6, 9.0.6]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-x64", "version": "[9.0.6, 9.0.6]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[9.0.6, 9.0.6]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[9.0.6, 9.0.6]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-x64": {"#import": []}, "win-x64": {"#import": []}}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Exceptions\\MyGeotabAPIAdapter.Exceptions.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Exceptions\\MyGeotabAPIAdapter.Exceptions.csproj", "projectName": "MyGeotabAPIAdapter.Exceptions", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Exceptions\\MyGeotabAPIAdapter.Exceptions.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Exceptions\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Geospatial\\MyGeotabAPIAdapter.Geospatial.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Geospatial\\MyGeotabAPIAdapter.Geospatial.csproj", "projectName": "MyGeotabAPIAdapter.Geospatial", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Geospatial\\MyGeotabAPIAdapter.Geospatial.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Geospatial\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Logging\\MyGeotabAPIAdapter.Logging.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Logging\\MyGeotabAPIAdapter.Logging.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"NLog.Extensions.Logging": {"target": "Package", "version": "[5.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.GeotabObjectMappers\\MyGeotabAPIAdapter.GeotabObjectMappers.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.GeotabObjectMappers\\MyGeotabAPIAdapter.GeotabObjectMappers.csproj", "projectName": "MyGeotabAPIAdapter.GeotabObjectMappers", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.GeotabObjectMappers\\MyGeotabAPIAdapter.GeotabObjectMappers.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.GeotabObjectMappers\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database\\MyGeotabAPIAdapter.Database.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database\\MyGeotabAPIAdapter.Database.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Helpers\\MyGeotabAPIAdapter.Helpers.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Helpers\\MyGeotabAPIAdapter.Helpers.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.MyGeotabAPI\\MyGeotabAPIAdapter.MyGeotabAPI.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.MyGeotabAPI\\MyGeotabAPIAdapter.MyGeotabAPI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Geotab.Checkmate.ObjectModel": {"target": "Package", "version": "[11.68.266, )"}, "NLog.Extensions.Logging": {"target": "Package", "version": "[5.5.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Helpers\\MyGeotabAPIAdapter.Helpers.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Helpers\\MyGeotabAPIAdapter.Helpers.csproj", "projectName": "MyGeotabAPIAdapter.Helpers", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Helpers\\MyGeotabAPIAdapter.Helpers.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Helpers\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Logging\\MyGeotabAPIAdapter.Logging.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Logging\\MyGeotabAPIAdapter.Logging.csproj", "projectName": "MyGeotabAPIAdapter.Logging", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Logging\\MyGeotabAPIAdapter.Logging.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Logging\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Exceptions\\MyGeotabAPIAdapter.Exceptions.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Exceptions\\MyGeotabAPIAdapter.Exceptions.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"NLog.Extensions.Logging": {"target": "Package", "version": "[5.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.MyGeotabAPI\\MyGeotabAPIAdapter.MyGeotabAPI.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.MyGeotabAPI\\MyGeotabAPIAdapter.MyGeotabAPI.csproj", "projectName": "MyGeotabAPIAdapter.MyGeotabAPI", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.MyGeotabAPI\\MyGeotabAPIAdapter.MyGeotabAPI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.MyGeotabAPI\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Exceptions\\MyGeotabAPIAdapter.Exceptions.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Exceptions\\MyGeotabAPIAdapter.Exceptions.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Helpers\\MyGeotabAPIAdapter.Helpers.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Helpers\\MyGeotabAPIAdapter.Helpers.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Logging\\MyGeotabAPIAdapter.Logging.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Logging\\MyGeotabAPIAdapter.Logging.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Geotab.Checkmate.ObjectModel": {"target": "Package", "version": "[11.68.266, )"}, "NLog.Extensions.Logging": {"target": "Package", "version": "[5.5.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Polly": {"target": "Package", "version": "[8.5.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Tests\\MyGeotabAPIAdapter.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Tests\\MyGeotabAPIAdapter.Tests.csproj", "projectName": "MyGeotabAPIAdapter.Tests", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Tests\\MyGeotabAPIAdapter.Tests.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Tests\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Configuration\\MyGeotabAPIAdapter.Configuration.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Configuration\\MyGeotabAPIAdapter.Configuration.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database.EntityPersisters\\MyGeotabAPIAdapter.Database.EntityPersisters.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database.EntityPersisters\\MyGeotabAPIAdapter.Database.EntityPersisters.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database\\MyGeotabAPIAdapter.Database.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database\\MyGeotabAPIAdapter.Database.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.DataOptimizer\\MyGeotabAPIAdapter.DataOptimizer.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.DataOptimizer\\MyGeotabAPIAdapter.DataOptimizer.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Exceptions\\MyGeotabAPIAdapter.Exceptions.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Exceptions\\MyGeotabAPIAdapter.Exceptions.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Geospatial\\MyGeotabAPIAdapter.Geospatial.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Geospatial\\MyGeotabAPIAdapter.Geospatial.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Helpers\\MyGeotabAPIAdapter.Helpers.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Helpers\\MyGeotabAPIAdapter.Helpers.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Logging\\MyGeotabAPIAdapter.Logging.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Logging\\MyGeotabAPIAdapter.Logging.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter\\MyGeotabAPIAdapter.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter\\MyGeotabAPIAdapter.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.14.1, )"}, "Moq": {"target": "Package", "version": "[4.18.4, )"}, "Oracle.ManagedDataAccess.Core": {"target": "Package", "version": "[23.8.0, )"}, "coverlet.collector": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.4, )"}, "xunit": {"target": "Package", "version": "[2.9.3, )"}, "xunit.runner.console": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.9.3, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[3.1.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter\\MyGeotabAPIAdapter.csproj": {"version": "3.8.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter\\MyGeotabAPIAdapter.csproj", "projectName": "MyGeotabAPIAdapter", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter\\MyGeotabAPIAdapter.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Configuration\\MyGeotabAPIAdapter.Configuration.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Configuration\\MyGeotabAPIAdapter.Configuration.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database.EntityPersisters\\MyGeotabAPIAdapter.Database.EntityPersisters.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database.EntityPersisters\\MyGeotabAPIAdapter.Database.EntityPersisters.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database\\MyGeotabAPIAdapter.Database.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Database\\MyGeotabAPIAdapter.Database.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Geospatial\\MyGeotabAPIAdapter.Geospatial.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Geospatial\\MyGeotabAPIAdapter.Geospatial.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.GeotabObjectMappers\\MyGeotabAPIAdapter.GeotabObjectMappers.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.GeotabObjectMappers\\MyGeotabAPIAdapter.GeotabObjectMappers.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Helpers\\MyGeotabAPIAdapter.Helpers.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Helpers\\MyGeotabAPIAdapter.Helpers.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Logging\\MyGeotabAPIAdapter.Logging.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.Logging\\MyGeotabAPIAdapter.Logging.csproj"}, "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.MyGeotabAPI\\MyGeotabAPIAdapter.MyGeotabAPI.csproj": {"projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\919\\local-deployment\\mygeotab-api-adapter\\MyGeotabAPIAdapter.MyGeotabAPI\\MyGeotabAPIAdapter.MyGeotabAPI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Geotab.Checkmate.ObjectModel": {"target": "Package", "version": "[11.68.266, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[9.0.5, )"}, "Microsoft.NETCore.Platforms": {"target": "Package", "version": "[7.0.4, )"}, "NLog.Extensions.Logging": {"target": "Package", "version": "[5.5.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "Oracle.ManagedDataAccess.Core": {"target": "Package", "version": "[23.8.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.linux-x64", "version": "[9.0.6, 9.0.6]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[9.0.6, 9.0.6]"}, {"name": "Microsoft.NETCore.App.Host.linux-x64", "version": "[9.0.6, 9.0.6]"}, {"name": "Microsoft.NETCore.App.Runtime.linux-x64", "version": "[9.0.6, 9.0.6]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[9.0.6, 9.0.6]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[9.0.6, 9.0.6]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"linux-x64": {"#import": []}, "win-x64": {"#import": []}}}}}