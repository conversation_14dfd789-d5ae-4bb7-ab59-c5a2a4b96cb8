server:
  http_listen_port: 9080
  grpc_listen_port: 0

positions:
  filename: /tmp/positions.yaml

clients:
  - url: http://loki:3100/loki/api/v1/push

scrape_configs:
  # Docker container logs
  - job_name: docker
    docker_sd_configs:
      - host: unix:///var/run/docker.sock
        refresh_interval: 5s
        filters:
          - name: label
            values: ["logging=promtail"]
    relabel_configs:
      - source_labels: ['__meta_docker_container_name']
        regex: '/(.*)' 
        target_label: 'container_name'
      - source_labels: ['__meta_docker_container_log_stream']
        target_label: 'logstream'
      - source_labels: ['__meta_docker_container_label_com_docker_compose_service']
        target_label: 'service_name'
    pipeline_stages:
      - json:
          expressions:
            output: log
            stream: stream
            attrs:
      - json:
          expressions:
            tag: attrs.tag
          source: attrs
      - regex:
          expression: (?P<container_name>(?:[^|]*))\|
          source: tag
      - timestamp:
          format: RFC3339Nano
          source: time

  # System logs
  - job_name: syslog
    static_configs:
      - targets:
          - localhost
        labels:
          job: syslog
          __path__: /var/log/syslog

  # Docker daemon logs
  - job_name: dockerd
    static_configs:
      - targets:
          - localhost
        labels:
          job: dockerd
          __path__: /var/log/docker.log

  # MyGeotab Adapter specific logs (if available)
  - job_name: mygeotab-adapter
    static_configs:
      - targets:
          - localhost
        labels:
          job: mygeotab-adapter
          service_name: mygeotab-adapter
          __path__: /var/log/mygeotab/*.log

  # Application logs from containers
  - job_name: containers
    docker_sd_configs:
      - host: unix:///var/run/docker.sock
        refresh_interval: 5s
    relabel_configs:
      - source_labels: ['__meta_docker_container_name']
        regex: '/(.*)' 
        target_label: 'container_name'
      - source_labels: ['__meta_docker_container_label_com_docker_compose_service']
        target_label: 'service_name'
      - source_labels: ['__meta_docker_container_label_com_docker_compose_project']
        target_label: 'compose_project'
      - source_labels: ['__meta_docker_container_log_stream']
        target_label: 'stream'
    pipeline_stages:
      - match:
          selector: '{container_name="postgres"}'
          stages:
            - regex:
                expression: '(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}.\d{3}) (?P<timezone>\w+) \[(?P<pid>\d+)\] (?P<level>\w+):\s+(?P<message>.*)'
            - labels:
                level:
                pid:
      - match:
          selector: '{container_name="grafana"}'
          stages:
            - json:
                expressions:
                  level: lvl
                  message: msg
                  logger: logger
            - labels:
                level:
                logger:
      - match:
          selector: '{container_name="netdata"}'
          stages:
            - regex:
                expression: '(?P<timestamp>\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}): (?P<component>\w+): (?P<level>\w+): (?P<message>.*)'
            - labels:
                level:
                component:
