﻿[*.cs]

# CS8618: Non-nullable field must contain a non-null value when exiting constructor. Consider declaring as nullable.
dotnet_diagnostic.CS8618.severity = silent

# CS8600: Converting null literal or possible null value to non-nullable type.
dotnet_diagnostic.CS8600.severity = silent

# CS8602: Dereference of a possibly null reference.
dotnet_diagnostic.CS8602.severity = silent

# CS8601: Possible null reference assignment.
dotnet_diagnostic.CS8601.severity = silent

# CS8604: Possible null reference argument.
dotnet_diagnostic.CS8604.severity = silent

# CS8629: Nullable value type may be null.
dotnet_diagnostic.CS8629.severity = silent
