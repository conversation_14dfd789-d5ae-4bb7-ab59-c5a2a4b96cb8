<?xml version="1.0" encoding="utf-8" ?>
<!-- X<PERSON> manual extracted from package NLog.Schema: https://www.nuget.org/packages/NLog.Schema-->
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd" xsi:schemaLocation="NLog NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogFile="LOG-MyGeotab_API_Adapter-internal.log"
      internalLogLevel="Error" >

  <!-- the targets to write to -->
  <targets>
    <!-- write logs to file -->
    <target xsi:type="File" name="target1" fileName="LOG-MyGeotab_API_Adapter.log" maxArchiveFiles="100" archiveAboveSize="5120000" archiveEvery="Day">
      <layout xsi:type="CsvLayout" delimiter="Pipe" withHeader="true">
        <column name="Time" layout="${longdate}" />
        <column name="Level" layout="${level:upperCase=true}"/>
        <column name="Message" layout="${message}" />
        <column name="Exception" layout="${exception}"/>
        <column name="Logger" layout="${logger}" />
        <column name="All Event Properties" layout="${all-event-properties}" />
      </layout>  
    </target>
    <target xsi:type="Console" name="target2"
            layout="${date}|${level:uppercase=true}|${message} ${exception}|${logger}|${all-event-properties}" />
  </targets>

  <!-- rules to map from logger name to target -->
  <rules>
    <logger name="*" minlevel="Info" writeTo="target1,target2" />
  </rules>
</nlog>