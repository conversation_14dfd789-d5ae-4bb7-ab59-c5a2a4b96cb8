﻿// This file is used by Code Analysis to maintain SuppressMessage
// attributes that are applied to this project.
// Project-level suppressions either have no target or are given
// a specific target and scoped to a namespace, type, member, etc.

using System.Diagnostics.CodeAnalysis;

[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.ProcessorTracker.InitializeDbOProcessorTrackingListAsync~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.BinaryDataProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.DeviceProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.DiagnosticProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.DriverChangeProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.FaultDataOptimizer.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.FaultDataProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.LogRecordProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.Orchestrator.WaitForConnectivityRestorationAsync~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.StatusDataOptimizer.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.StatusDataProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.UserProcessor.ExecuteAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0060:Remove unused parameter", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.BinaryDataProcessor.InitializeOrUpdateCachesAsync(System.Threading.CancellationTokenSource)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0060:Remove unused parameter", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.DeviceProcessor.InitializeOrUpdateCachesAsync(System.Threading.CancellationTokenSource)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0060:Remove unused parameter", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.DiagnosticProcessor.InitializeOrUpdateCachesAsync(System.Threading.CancellationTokenSource)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0060:Remove unused parameter", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.DriverChangeProcessor.InitializeOrUpdateCachesAsync(System.Threading.CancellationTokenSource)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0060:Remove unused parameter", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.FaultDataProcessor.InitializeOrUpdateCachesAsync(System.Threading.CancellationTokenSource)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0060:Remove unused parameter", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.LogRecordProcessor.InitializeOrUpdateCachesAsync(System.Threading.CancellationTokenSource)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0060:Remove unused parameter", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.UserProcessor.InitializeOrUpdateCachesAsync(System.Threading.CancellationTokenSource)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.BinaryDataProcessor.GetBinaryTypeIdAsync(System.String,System.Threading.CancellationTokenSource)~System.Threading.Tasks.Task{System.Int64}")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.BinaryDataProcessor.GetControllerIdAsync(System.String,System.Threading.CancellationTokenSource)~System.Threading.Tasks.Task{System.Int64}")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.BinaryDataProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.DeviceProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.DiagnosticProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.DriverChangeProcessor.GetDriverChangeTypeIdAsync(System.String,System.Threading.CancellationTokenSource)~System.Threading.Tasks.Task{System.Int64}")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.DriverChangeProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.FaultDataOptimizer.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.FaultDataProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.LogRecordProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.StatusDataOptimizer.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.StatusDataProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
[assembly: SuppressMessage("Style", "IDE0063:Use simple 'using' statement", Justification = "<Pending>", Scope = "member", Target = "~M:MyGeotabAPIAdapter.DataOptimizer.Services.UserProcessor.StartAsync(System.Threading.CancellationToken)~System.Threading.Tasks.Task")]
