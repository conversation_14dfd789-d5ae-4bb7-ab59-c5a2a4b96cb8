﻿################################################################################
# This .gitignore file was automatically created by Microsoft(R) Visual Studio.
################################################################################

/.vs
*.log
*.user
/MyGeotabAPIAdapter/bin
/MyGeotabAPIAdapter/obj
/MyGeotabAPIAdapter/appsettings.Development.json
/MyGeotabAPIAdapter/MyGeotabAPIAdapter.csproj.user
/MyGeotabAPIAdapter/Properties/PublishProfiles/MyGeotabAPIAdapter_Release_SCD_Target_win-x64.pubxml.user
/MyGeotabAPIAdapter/Properties/PublishProfiles/MyGeotabAPIAdapter_Release_SCD_Target_ubuntu.19.10-x64.pubxml.user
/MyGeotabAPIAdapter/Properties/PublishProfiles/MyGeotabAPIAdapter_Release_SCD_Target_linux-x64.pubxml.user
/MyGeotabAPIAdapter.Configuration/bin
/MyGeotabAPIAdapter.Configuration/obj
/MyGeotabAPIAdapter.Database/bin
/MyGeotabAPIAdapter.Database/obj
/MyGeotabAPIAdapter.Database.EntityPersisters/bin
/MyGeotabAPIAdapter.Database.EntityPersisters/obj
/MyGeotabAPIAdapter.DataOptimizer/bin
/MyGeotabAPIAdapter.DataOptimizer/obj
/MyGeotabAPIAdapter.DataOptimizer/appsettings.Development.json
/MyGeotabAPIAdapter.DataOptimizer.EntityBrokers/bin
/MyGeotabAPIAdapter.DataOptimizer.EntityBrokers/obj
/MyGeotabAPIAdapter.DataOptimizer.ObjectMappers/bin
/MyGeotabAPIAdapter.DataOptimizer.ObjectMappers/obj
/MyGeotabAPIAdapter.Exceptions/bin
/MyGeotabAPIAdapter.Exceptions/obj
/MyGeotabAPIAdapter.Geospatial/bin
/MyGeotabAPIAdapter.Geospatial/obj
/MyGeotabAPIAdapter.GeotabObjectMappers/bin
/MyGeotabAPIAdapter.GeotabObjectMappers/obj
/MyGeotabAPIAdapter.Helpers/bin
/MyGeotabAPIAdapter.Helpers/obj
/MyGeotabAPIAdapter.Logging/bin
/MyGeotabAPIAdapter.Logging/obj
/MyGeotabAPIAdapter.MyGeotabAPI/bin
/MyGeotabAPIAdapter.MyGeotabAPI/obj
/MyGeotabAPIAdapter.Tests/bin
/MyGeotabAPIAdapter.Tests/obj