﻿namespace MyGeotabAPIAdapter
{
    /// <summary>
    /// Lists all of the available services.
    /// </summary>
    public enum AdapterService { BinaryDataProcessor, BinaryDataProcessor2, ChargeEventProcessor, ChargeEventProcessor2, ControllerProcessor, ControllerProcessor2, DatabaseMaintenanceService2, DebugDataProcessor, DeviceProcessor, DeviceProcessor2, DeviceStatusInfoProcessor, DeviceStatusInfoProcessor2, DiagnosticProcessor, DiagnosticProcessor2, DriverChangeProcessor, DriverChangeProcessor2, DutyStatusAvailabilityProcessor, DutyStatusLogProcessor, DVIRLogManipulator, DVIRLogProcessor, DVIRLogProcessor2, ExceptionEventProcessor, ExceptionEventProcessor2, FailureModeProcessor, FailureModeProcessor2, FaultDataLocationService2, FaultDataProcessor, FaultDataProcessor2, GroupProcessor, GroupProcessor2, LogRecordProcessor, LogRecordProcessor2, OVDSClientWorker, RuleProcessor, RuleProcessor2, StatusDataLocationService2, StatusDataProcessor, StatusDataProcessor2, TripProcessor, TripProcessor2, UnitOfMeasureProcessor, UnitOfMeasureProcessor2, UserProcessor, UserProcessor2, ZoneProcessor, ZoneProcessor2, ZoneTypeProcessor, ZoneTypeProcessor2, NullValue }
}
